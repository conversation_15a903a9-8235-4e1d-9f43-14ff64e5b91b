[Unit]
Description=Django Q Cluster
After=network.target
Requires=network.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/mcshop
Environment=PATH=/home/<USER>/mcshop/.venv/bin
ExecStart=/home/<USER>/mcshop/start_djangoq.bash
Restart=always
RestartSec=10

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=django-q

[Install]
WantedBy=multi-user.target
