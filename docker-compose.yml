version: '3.8'

services:
  web:
    build: .
    ports:
      - "4000:4000"
    volumes:
      - .:/app
    environment:
      - DJANGO_SETTINGS_MODULE=mcshop.settings
    command: gunicorn --workers=2 -b 0.0.0.0:4000 mcshop.wsgi
    depends_on:
      - db

  django-q:
    build: .
    volumes:
      - .:/app
    environment:
      - DJANGO_SETTINGS_MODULE=mcshop.settings
    command: python manage.py qcluster
    depends_on:
      - db
    restart: always

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: mcshop
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
