#!/usr/bin/env python
"""
Demonstration script for the new admin actions
"""
import os
import sys
import django
from unittest.mock import patch, Mock

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from django.test import RequestFactory
from django.contrib.admin.sites import AdminSite
from django.contrib.auth.models import User
from django.contrib.messages.storage.fallback import FallbackStorage
from shop.models import MinecraftServer, Category, Item, Purchase, PurchaseItem, CommandJob
from shop.admin import PurchaseAdmin, PurchaseItemAdmin, CommandJobAdmin


def demo_admin_actions():
    """Demonstrate the new admin actions functionality"""
    print("=== Admin Actions Demo: Run Pending Orders ===\n")
    
    # Create or get test data
    server, created = MinecraftServer.objects.get_or_create(
        name="demo_server",
        defaults={
            "display_name": "Demo Server",
            "ip": "127.0.0.1",
            "port": 25565,
            "rcon_port": 25575,
            "api_port": 28535,
            "api_token": "demo_token",
            "enabled": True,
            "published": True
        }
    )
    print(f"✅ Server: {server.name} ({'created' if created else 'found'})")
    
    category, created = Category.objects.get_or_create(
        name="demo_category",
        defaults={
            "display_name": "Demo Category",
            "enabled": True,
            "published": True
        }
    )
    print(f"✅ Category: {category.name} ({'created' if created else 'found'})")
    
    # Create items
    item_verified, created = Item.objects.get_or_create(
        name="admin_verified_item",
        defaults={
            "display_name": "Admin Verified Item",
            "description": "Item requiring user verification",
            "category": category,
            "price": 100,
            "commands": "give {username} diamond 1",
            "minecraft_server": server,
            "require_user_verification": True,
            "enabled": True,
            "published": True
        }
    )
    print(f"✅ Item with verification: {item_verified.name} ({'created' if created else 'found'})")
    
    item_unverified, created = Item.objects.get_or_create(
        name="admin_unverified_item",
        defaults={
            "display_name": "Admin Unverified Item",
            "description": "Item not requiring user verification",
            "category": category,
            "price": 50,
            "commands": "give {username} gold_ingot 1",
            "minecraft_server": server,
            "require_user_verification": False,
            "enabled": True,
            "published": True
        }
    )
    print(f"✅ Item without verification: {item_unverified.name} ({'created' if created else 'found'})")
    
    # Create purchases
    purchase_1 = Purchase.objects.create(
        minecraft_username="admin_user_1",
        state=Purchase.State.SUCCESSFUL
    )
    
    purchase_2 = Purchase.objects.create(
        minecraft_username="admin_user_2",
        state=Purchase.State.SUCCESSFUL
    )
    print(f"✅ Created purchases for users: {purchase_1.minecraft_username}, {purchase_2.minecraft_username}")
    
    # Create purchase items
    purchase_item_verified = PurchaseItem.objects.create(
        purchase=purchase_1,
        item=item_verified,
        quantity=1
    )
    
    purchase_item_unverified = PurchaseItem.objects.create(
        purchase=purchase_2,
        item=item_unverified,
        quantity=1
    )
    print(f"✅ Created purchase items")
    
    # Create command jobs
    command_job_verified = CommandJob.objects.create(
        purchase_item=purchase_item_verified,
        command_text="give admin_user_1 diamond 1",
        sequence_order=1,
        state=CommandJob.State.PENDING
    )
    
    command_job_unverified = CommandJob.objects.create(
        purchase_item=purchase_item_unverified,
        command_text="give admin_user_2 gold_ingot 1",
        sequence_order=1,
        state=CommandJob.State.PENDING
    )
    print(f"✅ Created pending command jobs")
    
    # Set up admin classes and request
    factory = RequestFactory()
    user = User.objects.get_or_create(username='admin', defaults={'is_superuser': True})[0]
    site = AdminSite()
    
    purchase_admin = PurchaseAdmin(Purchase, site)
    purchase_item_admin = PurchaseItemAdmin(PurchaseItem, site)
    command_job_admin = CommandJobAdmin(CommandJob, site)
    
    def create_request():
        """Create a mock request with messages support"""
        request = factory.post('/')
        request.user = user
        setattr(request, 'session', {})
        messages = FallbackStorage(request)
        setattr(request, '_messages', messages)
        return request
    
    print("\n=== Testing Admin Actions ===")
    
    # Test 1: PurchaseAdmin - User present
    print("\n1. Testing PurchaseAdmin.run_pending_orders (user present):")
    request = create_request()
    queryset = Purchase.objects.filter(id=purchase_1.id)
    
    with patch('shop.admin.is_user_present_on_server', return_value=True) as mock_user_present:
        with patch('shop.admin.async_task') as mock_async_task:
            purchase_admin.run_pending_orders(request, queryset)
            
            print(f"   ✅ User verification checked: {mock_user_present.called}")
            print(f"   ✅ Task queued: {mock_async_task.called}")
            if mock_async_task.called:
                print(f"   ✅ Task: {mock_async_task.call_args[0][0]} with purchase ID {mock_async_task.call_args[0][1]}")
    
    # Test 2: PurchaseAdmin - User not present
    print("\n2. Testing PurchaseAdmin.run_pending_orders (user not present):")
    request = create_request()
    queryset = Purchase.objects.filter(id=purchase_1.id)
    
    with patch('shop.admin.is_user_present_on_server', return_value=False) as mock_user_present:
        with patch('shop.admin.async_task') as mock_async_task:
            purchase_admin.run_pending_orders(request, queryset)
            
            print(f"   ✅ User verification checked: {mock_user_present.called}")
            print(f"   ❌ Task NOT queued: {not mock_async_task.called}")
    
    # Test 3: PurchaseAdmin - No verification required
    print("\n3. Testing PurchaseAdmin.run_pending_orders (no verification required):")
    request = create_request()
    queryset = Purchase.objects.filter(id=purchase_2.id)
    
    with patch('shop.admin.async_task') as mock_async_task:
        purchase_admin.run_pending_orders(request, queryset)
        
        print(f"   ✅ Task queued directly: {mock_async_task.called}")
        if mock_async_task.called:
            print(f"   ✅ Task: {mock_async_task.call_args[0][0]} with purchase ID {mock_async_task.call_args[0][1]}")
    
    # Test 4: PurchaseItemAdmin - User present
    print("\n4. Testing PurchaseItemAdmin.run_pending_orders (user present):")
    request = create_request()
    queryset = PurchaseItem.objects.filter(id=purchase_item_verified.id)
    
    with patch('shop.admin.is_user_present_on_server', return_value=True) as mock_user_present:
        with patch('shop.admin.async_task') as mock_async_task:
            purchase_item_admin.run_pending_orders(request, queryset)
            
            print(f"   ✅ User verification checked: {mock_user_present.called}")
            print(f"   ✅ Task queued: {mock_async_task.called}")
            if mock_async_task.called:
                print(f"   ✅ Task: {mock_async_task.call_args[0][0]} with purchase item ID {mock_async_task.call_args[0][1]}")
    
    # Test 5: CommandJobAdmin - User present
    print("\n5. Testing CommandJobAdmin.run_pending_orders (user present):")
    request = create_request()
    queryset = CommandJob.objects.filter(id=command_job_verified.id)
    
    with patch('shop.admin.is_user_present_on_server', return_value=True) as mock_user_present:
        with patch('shop.admin.async_task') as mock_async_task:
            command_job_admin.run_pending_orders(request, queryset)
            
            print(f"   ✅ User verification checked: {mock_user_present.called}")
            print(f"   ✅ Task queued: {mock_async_task.called}")
            if mock_async_task.called:
                print(f"   ✅ Task: {mock_async_task.call_args[0][0]} with command job ID {mock_async_task.call_args[0][1]}")
    
    # Test 6: CommandJobAdmin - User not present
    print("\n6. Testing CommandJobAdmin.run_pending_orders (user not present):")
    request = create_request()
    queryset = CommandJob.objects.filter(id=command_job_verified.id)
    
    with patch('shop.admin.is_user_present_on_server', return_value=False) as mock_user_present:
        with patch('shop.admin.async_task') as mock_async_task:
            command_job_admin.run_pending_orders(request, queryset)
            
            print(f"   ✅ User verification checked: {mock_user_present.called}")
            print(f"   ❌ Task NOT queued: {not mock_async_task.called}")
    
    print("\n=== Demo Complete ===")
    print("✅ All admin actions working as expected!")
    print("\nKey Features:")
    print("- PurchaseAdmin: Runs all pending orders for selected purchases")
    print("- PurchaseItemAdmin: Runs pending orders for selected purchase items")
    print("- CommandJobAdmin: Runs pending command jobs individually")
    print("- All actions check user verification when required")
    print("- Actions skip execution if user verification fails")
    print("- Proper admin messages are shown for success/failure/skipped items")


if __name__ == '__main__':
    demo_admin_actions()
