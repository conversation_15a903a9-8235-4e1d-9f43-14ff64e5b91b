# Support User System Guide

## Overview

The support user system allows designated staff members to search for purchases by reference ID and perform limited administrative actions without having access to all purchase data.

## Features

### For Support Users:
- **Limited Search**: Can only search by exact reference ID (`ref_id`) and mobile number
- **Restricted List View**: Shows only essential fields
- **Readonly Access**: All fields are readonly in detail view
- **Limited Actions**: Can only retry failed commands and run pending orders
- **No Save Buttons**: Save, Save and Continue, Save and Add Another buttons are disabled
- **Purchase Items Access**: Can search purchase items by purchase reference ID
- **No Add/Delete**: Cannot add new records or delete existing ones

### For Admin Users:
- **Full Access**: Complete access to all purchase data and functionality
- **All Actions**: Access to all administrative actions
- **Complete View**: Can see all fields and related data

## Setup Instructions

### 1. Create Support User

Using the management command:
```bash
python manage.py create_support_user username --email <EMAIL> --password password123
```

### 2. Manual Assignment

Through Django Admin:
1. Go to **Users** → Select user → Edit
2. In the **Profile** section, check **"Is support"**
3. Save the user

Or through **User Profiles**:
1. Go to **User Profiles** → Find user profile
2. Check **"Is support"** checkbox
3. Save

### 3. Grant Permissions

In Django Admin, go to **Users** → Select support user → **User permissions**:

**Required permissions:**
- `shop | purchase | Can view purchase`
- `shop | purchase | Can change purchase`

**Optional permissions** (for admin actions):
- `shop | command job | Can view command job`
- `shop | command job | Can change command job`

## Support User Interface

### List View
**Displayed Fields:**
- Reference ID
- Items (summary)
- Minecraft Username
- State
- Derived State
- Created At
- Payment Succeeded At
- Referrer Info

**Available Filters:**
- Created At
- Payment Succeeded At

**Search Fields:**
- Reference ID (exact match)
- Mobile Number

### Detail View
**Purchase Information:**
- Reference ID (readonly)
- Minecraft Username (readonly)
- Mobile Number (readonly)
- State (readonly)
- Created At (readonly)
- Payment Succeeded At (readonly)

**Payment Information:**
- Zarinpal Code (readonly)

**Referral Information:**
- Referrer (readonly)

### Available Actions
- **Retry Failed Commands**: Retry failed command executions for selected purchases
- **Run Pending Orders**: Execute pending commands with user verification

## Usage Workflow

### For Support Staff:

1. **Login** to Django Admin with support user credentials
2. **Navigate** to Shop → Purchases or Shop → Purchase Items
3. **Search** using the exact 8-digit reference ID provided by the customer
4. **View** purchase/item details in readonly mode (no save buttons)
5. **Execute Actions** if needed:
   - Retry failed commands if there are execution errors
   - Run pending orders if commands haven't been executed

### Purchase Items Access:
- Navigate to Shop → Purchase Items
- Search by purchase reference ID to see related items
- View item details and command execution status
- Execute retry/run actions on specific items

### Example Search:
- Customer provides reference ID: `12345678`
- Support user enters `12345678` in search box
- System shows the matching purchase(s) or purchase item(s)
- Support user can view details and run actions (no editing allowed)

## Security Features

- **No Browse Access**: Support users cannot browse all purchases
- **Search-Only Access**: Must know exact reference ID to find purchases
- **Readonly Fields**: Cannot modify purchase data
- **Limited Actions**: Only command-related actions available
- **No Purchase Items**: Cannot see detailed item information
- **Permission-Based**: Requires explicit permission assignment

## Troubleshooting

### Support User Can't See Purchase Details
1. Verify user has `is_support = True` in their profile
2. Check user has required permissions:
   - `shop | purchase | Can view purchase`
   - `shop | purchase | Can change purchase`
3. Ensure user is searching with exact reference ID

### Support User Can't Execute Actions
1. Verify user has change permissions
2. Check that the purchase has failed or pending commands
3. Ensure user is staff member (`is_staff = True`)

### Search Not Working
1. Confirm search term is exact reference ID (case-insensitive)
2. Verify purchase exists in database
3. Check user is using correct search field (ref_id or mobile_number)

## Technical Implementation

The system uses Django admin customization methods:
- `get_queryset()`: Filters purchases based on search
- `get_list_display()`: Customizes visible fields
- `get_fieldsets()`: Restricts detail view fields
- `get_actions()`: Limits available actions
- `get_readonly_fields()`: Makes fields readonly
- `get_inlines()`: Removes purchase item details

All customizations are applied automatically based on the user's `is_support` flag in their UserProfile.
