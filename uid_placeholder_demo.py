#!/usr/bin/env python
"""
Demonstration script for the new {uid} placeholder functionality
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.utils import replace_command_placeholders

def demo_uid_placeholder():
    """Demonstrate the {uid} placeholder functionality"""
    print("=== {uid} Placeholder Demonstration ===\n")
    
    # Test cases
    test_cases = [
        "give {username} diamond 1",
        "give {username} diamond 1 {uid}",
        "setblock ~ ~ ~ chest{CustomName:'{\"text\":\"Chest_{uid}\"}'}",
        "give {username} diamond 1 {uid} && say Item {uid} given to {username}",
        "command {uid} and {uid}",  # Multiple UIDs
    ]
    
    username = "testuser"
    
    for i, command in enumerate(test_cases, 1):
        print(f"Test Case {i}:")
        print(f"  Original: {command}")
        
        # Process the command multiple times to show uniqueness
        for j in range(3):
            processed = replace_command_placeholders(command, username)
            print(f"  Run {j+1}:   {processed}")
        
        print()

if __name__ == "__main__":
    demo_uid_placeholder()
