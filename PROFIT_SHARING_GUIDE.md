# Profit-Sharing System for ContentCreators

This document explains the newly implemented profit-sharing system for ContentCreators in the Django shop application.

## Overview

The profit-sharing system automatically calculates and tracks commission payments for ContentCreators when customers make purchases using their referral links. The system provides comprehensive tracking, settlement management, and reporting capabilities.

## Features

### ContentCreator Model Enhancements

1. **Commission Rate**: Each ContentCreator has a configurable commission rate (default: 20.00%)
2. **Admin Notes**: Internal notes field for administrative purposes
3. **Earnings Tracking**: Automatic calculation of total earned, settled, and unsettled commissions

### Purchase Model Enhancements

1. **Referral Commission**: Stores the calculated profit share amount
2. **Settlement Status**: Tracks whether commission has been settled (pending/settled)
3. **Settlement Timestamp**: Records when commission was marked as settled

### Business Logic Integration

1. **Automatic Calculation**: When a purchase is completed (payment_succeeded_at is set) and has a referrer, the system automatically calculates the commission
2. **Formula**: Commission = (Total Purchase Amount × Commission Rate) / 100
3. **Status Management**: New commissions start as 'pending' and can be marked as 'settled' by administrators

## Database Schema

### ContentCreator additions:
- `commission_rate`: DecimalField(max_digits=5, decimal_places=2, default=20.00)
- `admin_notes`: TextField(blank=True, null=True)

### Purchase additions:
- `referral_commission`: DecimalField(max_digits=10, decimal_places=2, null=True)
- `referral_settlement_status`: CharField with choices (null/pending/settled)
- `referral_settled_at`: DateTimeField(null=True)

### Calculated Properties

**ContentCreator properties:**
- `total_earned`: Sum of all referral commissions
- `total_settled`: Sum of settled commissions
- `total_unsettled`: Difference between earned and settled

## Admin Panel Features

### ContentCreator Admin

1. **Enhanced List View**: Shows commission rate and earnings summary
2. **Detailed Fieldsets**: Organized sections for basic info, commission settings, and earnings
3. **Read-only Earnings**: Displays total earned, settled, and unsettled amounts
4. **Commission Configuration**: Easy setup of commission rates and admin notes

### Purchase Admin

1. **Referral Information Display**: Shows referrer and commission details in list view
2. **Settlement Action**: "Mark referral payments as settled" bulk action
3. **Detailed Fieldsets**: Organized sections including referral information
4. **Filtering**: Filter by settlement status and referrer

### Settlement Management

**Purchase Admin Actions:**
1. **"Mark referral payments as settled"**:
   - Processes multiple purchases at once
   - Only affects purchases with 'pending' settlement status
   - Updates status to 'settled' and sets settlement timestamp
   - Provides feedback on number of settlements processed

2. **"Revert referral settlements (unsettle)"**:
   - Reverts settled purchases back to pending status
   - Clears settlement timestamp
   - Useful for correcting mistakes or handling disputes

3. **"Recalculate referral commissions"**:
   - Recalculates commissions for selected purchases
   - Useful when commission rates have been updated
   - Forces recalculation even if commission already exists

[//]: # (   - Resets settled purchases back to pending if recalculated (commented out)

**ContentCreator Admin Actions:**
1. **"Settle all unsettled purchases for selected creators"**:
   - Bulk settlement for all pending commissions of selected ContentCreators
   - Processes all unsettled purchases at once
   - Provides summary of settlements processed

## Usage Guide

### For Administrators

#### Setting Up ContentCreators

1. Navigate to Admin → Shop → Content creators
2. Edit a ContentCreator
3. Set the desired commission rate (e.g., 25.00 for 25%)
4. Add any internal notes in the admin notes field
5. Save the changes

#### Monitoring Earnings

1. In the ContentCreator list view, you can see:
   - Commission rate for each creator
   - Total earned amount
   - Total unsettled amount

2. Click on a ContentCreator to see detailed earnings breakdown

#### Processing Settlements

1. Navigate to Admin → Shop → Purchases
2. Filter by "Referral settlement status: Pending" to see unsettled commissions
3. Select the purchases you want to settle
4. Choose "Mark referral payments as settled" from the Actions dropdown
5. Click "Go" to process the settlements

### For Developers

#### Commission Calculation

The commission is calculated using explicit method calls for better control and predictability:

```python
# Primary method: Calculate and save commission
purchase = Purchase.objects.get(id=123)
purchase.calculate_and_set_referral_commission()

# Alternative: Calculate without saving
purchase.calculate_referral_commission()
purchase.save()

# Force recalculation (even if already calculated)
purchase.recalculate_referral_commission(force=True)
purchase.save()
```

**When commission is calculated:**
- Purchase has a referrer (ContentCreator)
- Purchase payment succeeds (payment_succeeded_at is set)
- Method is explicitly called (manual approach for better control)

#### Querying Earnings

```python
# Get all unsettled commissions for a content creator
creator = ContentCreator.objects.get(name="example")
unsettled_amount = creator.total_unsettled

# Get all pending settlements
pending_purchases = Purchase.objects.filter(
    referral_settlement_status='pending',
    referral_commission__isnull=False
)
```

## Testing

The system includes comprehensive tests covering:
- Commission calculation for single and multiple items
- Proper handling of purchases without referrers
- Earnings property calculations
- Prevention of commission recalculation

Run tests with:
```bash
python manage.py test shop.tests.test_profit_sharing
```

## Migration

The profit-sharing system was added via migration `0024_add_profit_sharing_system.py`. This migration:
- Adds new fields to ContentCreator and Purchase models
- Updates the referrer relationship to include related_name
- Is backward compatible with existing data

## Security Considerations

1. **Admin-Only Settlement**: Only administrators can mark commissions as settled
2. **Immutable Calculations**: Once calculated, commissions cannot be automatically recalculated
3. **Audit Trail**: Settlement timestamps provide audit trail for payments
4. **Decimal Precision**: Uses appropriate decimal precision for financial calculations

## Performance Notes

1. **Calculated Properties**: Earnings properties use database aggregation for efficiency
2. **Indexed Fields**: Settlement status and referrer fields are suitable for indexing
3. **Bulk Operations**: Settlement action processes multiple records efficiently

## Future Enhancements

Potential future improvements could include:
- Automated settlement scheduling
- Commission rate history tracking
- Payment integration for automatic payouts
- Detailed earnings reports and analytics
- Email notifications for settlements
