#!/usr/bin/env python
"""
Comprehensive test file for is_valid_minecraft_username function
"""
import os
import sys
import django
from unittest.mock import patch, Mock

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.minecraft import is_valid_minecraft_username
from shop.models import MinecraftServer


class TestIsValidMinecraftUsername:
    """Test class for is_valid_minecraft_username function"""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
    
    def assert_equal(self, actual, expected, message):
        """Helper method for assertions"""
        if actual == expected:
            print(f"✓ PASS: {message}")
            self.passed += 1
        else:
            print(f"✗ FAIL: {message} - Expected: {expected}, Got: {actual}")
            self.failed += 1
    
    def test_special_case_something(self):
        """Test the special case 'something' username"""
        print("\n1. Testing special case 'something':")
        result = is_valid_minecraft_username("something")
        self.assert_equal(result, True, "Special case 'something' should return True")
    
    def test_regex_validation_valid_usernames(self):
        """Test regex validation with valid usernames"""
        print("\n2. Testing regex validation - Valid usernames:")
        
        valid_usernames = [
            ("abc", "3 characters (minimum)"),
            ("test", "4 characters"),
            ("player123", "alphanumeric"),
            ("user_name", "with underscore"),
            ("Player_123", "mixed case with underscore"),
            ("a123456789012345", "16 characters (maximum)"),
            ("TestUser", "mixed case"),
            ("user123", "alphanumeric"),
            ("_underscore", "starting with underscore"),
            ("end_", "ending with underscore"),
        ]
        
        # Mock the HTTP request to avoid network calls
        mock_response = Mock()
        mock_response.json.return_value = {"success": True, "hasPlayedBefore": True}
        mock_response.raise_for_status.return_value = None
        
        with patch('shop.minecraft.requests.post', return_value=mock_response):
            for username, description in valid_usernames:
                result = is_valid_minecraft_username(username)
                self.assert_equal(result, True, f"'{username}' should pass regex validation ({description})")
    
    def test_regex_validation_invalid_usernames(self):
        """Test regex validation with invalid usernames"""
        print("\n3. Testing regex validation - Invalid usernames:")
        
        invalid_usernames = [
            # Too short
            ("ab", "2 characters (too short)"),
            ("a", "1 character (too short)"),
            ("", "empty string"),
            
            # Too long
            ("a12345678901234567", "17 characters (too long)"),
            ("verylongusernamethatexceedslimit", "very long username"),
            
            # Invalid characters
            ("user-name", "contains hyphen"),
            ("user.name", "contains dot"),
            ("user name", "contains space"),
            ("user@name", "contains @ symbol"),
            ("user#name", "contains # symbol"),
            ("user!name", "contains exclamation mark"),
            ("user+name", "contains plus sign"),
            ("user=name", "contains equals sign"),
            ("user(name)", "contains parentheses"),
            ("user[name]", "contains brackets"),
            ("user{name}", "contains braces"),
            ("user|name", "contains pipe"),
            ("user\\name", "contains backslash"),
            ("user/name", "contains forward slash"),
            ("user:name", "contains colon"),
            ("user;name", "contains semicolon"),
            ("user'name", "contains apostrophe"),
            ("user\"name", "contains quote"),
            ("user,name", "contains comma"),
            ("user<name>", "contains angle brackets"),
            ("user?name", "contains question mark"),
            ("user*name", "contains asterisk"),
            ("user%name", "contains percent"),
            ("user$name", "contains dollar sign"),
            ("user&name", "contains ampersand"),
            ("user^name", "contains caret"),
            ("user~name", "contains tilde"),
            ("user`name", "contains backtick"),
        ]
        
        for username, description in invalid_usernames:
            result = is_valid_minecraft_username(username)
            self.assert_equal(result, False, f"'{username}' should fail regex validation ({description})")
    
    def test_no_proxy_server(self):
        """Test behavior when no proxy server is configured"""
        print("\n4. Testing with no proxy server:")
        
        with patch('shop.models.MinecraftServer.objects.filter') as mock_filter:
            mock_filter.return_value.first.return_value = None
            
            result = is_valid_minecraft_username("validuser")
            self.assert_equal(result, False, "Should return False when no proxy server is found")
    
    def test_proxy_server_no_api_port(self):
        """Test behavior when proxy server has no API port"""
        print("\n5. Testing proxy server without API port:")

        mock_server = Mock()
        mock_server.name = "test_server"
        mock_server.api_port = None

        with patch('shop.models.MinecraftServer.objects.filter') as mock_filter:
            mock_filter.return_value.first.return_value = mock_server

            result = is_valid_minecraft_username("validuser")
            self.assert_equal(result, False, "Should return False when proxy server has no API port")
    
    def test_http_responses(self):
        """Test different HTTP response scenarios"""
        print("\n6. Testing HTTP response scenarios:")
        
        mock_server = Mock()
        mock_server.name = "test_server"
        mock_server.ip = "*************"
        mock_server.api_port = 28535
        mock_server.api_token = "test_token"
        
        with patch('shop.models.MinecraftServer.objects.filter') as mock_filter:
            mock_filter.return_value.first.return_value = mock_server
            
            # Test successful response with hasPlayedBefore=True
            mock_response_success = Mock()
            mock_response_success.json.return_value = {
                "success": True,
                "user": "testuser",
                "hasPlayedBefore": True
            }
            mock_response_success.raise_for_status.return_value = None
            
            with patch('shop.minecraft.requests.post', return_value=mock_response_success):
                result = is_valid_minecraft_username("testuser")
                self.assert_equal(result, True, "Should return True for existing user")
            
            # Test successful response with hasPlayedBefore=False
            mock_response_no_user = Mock()
            mock_response_no_user.json.return_value = {
                "success": True,
                "user": "newuser",
                "hasPlayedBefore": False
            }
            mock_response_no_user.raise_for_status.return_value = None
            
            with patch('shop.minecraft.requests.post', return_value=mock_response_no_user):
                result = is_valid_minecraft_username("newuser")
                self.assert_equal(result, False, "Should return False for non-existent user")
            
            # Test failed response (success=False)
            mock_response_failed = Mock()
            mock_response_failed.json.return_value = {
                "success": False,
                "error": "Server error"
            }
            mock_response_failed.raise_for_status.return_value = None
            
            with patch('shop.minecraft.requests.post', return_value=mock_response_failed):
                result = is_valid_minecraft_username("erroruser")
                self.assert_equal(result, False, "Should return False for failed server response")
    
    def test_url_building(self):
        """Test that URL is built correctly from proxy server configuration"""
        print("\n7. Testing URL building:")
        
        mock_server = Mock()
        mock_server.name = "test_server"
        mock_server.ip = "*************"
        mock_server.api_port = 28535
        mock_server.api_token = "test_token"
        
        mock_response = Mock()
        mock_response.json.return_value = {"success": True, "hasPlayedBefore": True}
        mock_response.raise_for_status.return_value = None
        
        with patch('shop.models.MinecraftServer.objects.filter') as mock_filter:
            mock_filter.return_value.first.return_value = mock_server
            
            with patch('shop.minecraft.requests.post') as mock_post:
                mock_post.return_value = mock_response
                
                is_valid_minecraft_username("testuser")
                
                # Verify the URL was built correctly
                expected_url = f"http://{mock_server.ip}:{mock_server.api_port}/"
                mock_post.assert_called_once()
                actual_url = mock_post.call_args[0][0]
                
                self.assert_equal(actual_url, expected_url, f"URL should be built as {expected_url}")
    
    def test_network_errors(self):
        """Test handling of network errors"""
        print("\n8. Testing network error handling:")
        
        mock_server = Mock()
        mock_server.name = "test_server"
        mock_server.ip = "*************"
        mock_server.api_port = 28535
        mock_server.api_token = "test_token"
        
        with patch('shop.models.MinecraftServer.objects.filter') as mock_filter:
            mock_filter.return_value.first.return_value = mock_server
            
            # Test connection timeout
            with patch('shop.minecraft.requests.post', side_effect=Exception("Connection timeout")):
                result = is_valid_minecraft_username("testuser")
                self.assert_equal(result, False, "Should return False on network error")
    
    def run_all_tests(self):
        """Run all tests"""
        print("Testing is_valid_minecraft_username function")
        print("=" * 60)
        
        self.test_special_case_something()
        self.test_regex_validation_valid_usernames()
        self.test_regex_validation_invalid_usernames()
        self.test_no_proxy_server()
        self.test_proxy_server_no_api_port()
        self.test_http_responses()
        self.test_url_building()
        self.test_network_errors()
        
        print("\n" + "=" * 60)
        print(f"Test Results: {self.passed} passed, {self.failed} failed")
        
        if self.failed == 0:
            print("🎉 All tests passed!")
            return True
        else:
            print(f"❌ {self.failed} tests failed")
            return False


def main():
    """Main test function"""
    tester = TestIsValidMinecraftUsername()
    success = tester.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
