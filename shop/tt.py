from mctools import QUERY<PERSON>lient
from mctools import <PERSON>ING<PERSON>lient
from mcstatus import JavaServer

# server = JavaServer.lookup("play.ucraft.ir")
server = JavaServer.lookup("37.32.27.33:25565")
status = server.status()
print(f"Server version: {status.version.name}")
print(f"Players online: {status.players.online}/{status.players.max}")

# ping = PINGClient('play.ucraft.ir')
# print(ping.get_stats())
#
# client = QUERYClient("37.32.27.33", 25565)
# print(client.get_basic_stats())
# client = QUERYClient("37.32.27.33", 25566)
# print(client.get_basic_stats())
# client = QUERYClient("37.32.27.33", 25577)
# print(client.get_basic_stats())
