import uuid

# In real setup, this should check an external Minecraft user DB
def is_valid_minecraft_username(username: str) -> bool:
    return username.isalnum() and 3 <= len(username) <= 16


def replace_command_placeholders(command: str, username: str) -> str:
    """
    Replace placeholders in command strings with actual values.

    Supported placeholders:
    - {username}: Replaced with the Minecraft username
    - {uid}: Replaced with a unique random identifier (each occurrence gets a different ID)

    Args:
        command: The command string containing placeholders
        username: The Minecraft username to replace {username} with

    Returns:
        str: The command with placeholders replaced
    """
    # Replace username placeholder
    processed_command = command.replace("{username}", username)

    # Replace each {uid} placeholder with a unique identifier
    # Generate a new UUID for each occurrence to ensure uniqueness
    while "{uid}" in processed_command:
        unique_id = str(uuid.uuid4()).replace('-', '')[:8]
        processed_command = processed_command.replace("{uid}", unique_id, 1)  # Replace only the first occurrence

    return processed_command