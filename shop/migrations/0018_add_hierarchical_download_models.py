# Generated by Django 5.2.1 on 2025-06-28 07:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0017_playercountsnapshot'),
    ]

    operations = [
        migrations.CreateModel(
            name='Platform',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('display_name', models.CharField(blank=True, max_length=150, null=True)),
                ('help', models.TextField(blank=True, help_text='Markdown help text for this platform', null=True)),
                ('download_url', models.URLField(blank=True, help_text='Direct download URL for this platform', max_length=500, null=True)),
                ('enabled', models.BooleanField(default=True)),
                ('published', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Launcher',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('display_name', models.CharField(blank=True, max_length=150, null=True)),
                ('help', models.TextField(blank=True, help_text='Markdown help text for this launcher', null=True)),
                ('download_url', models.URLField(blank=True, help_text='Direct download URL for this launcher', max_length=500, null=True)),
                ('enabled', models.BooleanField(default=True)),
                ('published', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('platform', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='launchers', to='shop.platform')),
            ],
            options={
                'ordering': ['name'],
                'unique_together': {('platform', 'name')},
            },
        ),
        migrations.CreateModel(
            name='Version',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('display_name', models.CharField(blank=True, max_length=150, null=True)),
                ('help', models.TextField(blank=True, help_text='Markdown help text for this version', null=True)),
                ('download_url', models.URLField(blank=True, help_text='Direct download URL for this version', max_length=500, null=True)),
                ('enabled', models.BooleanField(default=True)),
                ('published', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('launcher', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='versions', to='shop.launcher')),
            ],
            options={
                'ordering': ['name'],
                'unique_together': {('launcher', 'name')},
            },
        ),
    ]
