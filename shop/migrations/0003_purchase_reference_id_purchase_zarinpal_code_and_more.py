# Generated by Django 5.2.1 on 2025-05-31 15:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0002_category_description_category_display_name_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='purchase',
            name='reference_id',
            field=models.CharField(blank=True, help_text='8-digit human-readable reference ID', max_length=8, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='purchase',
            name='zarinpal_code',
            field=models.IntegerField(blank=True, help_text='Zarinpal verification code (100, 101, etc.)', null=True),
        ),
        migrations.AddField(
            model_name='purchase',
            name='zarinpal_verify_response',
            field=models.TextField(blank=True, help_text='Complete Zarinpal verify response as JSON', null=True),
        ),
        migrations.AlterField(
            model_name='purchase',
            name='z_response',
            field=models.TextField(blank=True, null=True),
        ),
    ]
