# Generated by Django 5.2.1 on 2025-06-28 13:02

import shop.storage
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0018_add_hierarchical_download_models'),
    ]

    operations = [
        migrations.DeleteModel(
            name='DownloadLink',
        ),
        migrations.AddField(
            model_name='launcher',
            name='image',
            field=models.ImageField(blank=True, help_text='Launcher icon image', null=True, storage=shop.storage.LauncherImageStorage(), upload_to=''),
        ),
        migrations.AddField(
            model_name='platform',
            name='image',
            field=models.ImageField(blank=True, help_text='Platform icon image', null=True, storage=shop.storage.PlatformImageStorage(), upload_to=''),
        ),
        migrations.AddField(
            model_name='version',
            name='image',
            field=models.ImageField(blank=True, help_text='Version icon image', null=True, storage=shop.storage.VersionImageStorage(), upload_to=''),
        ),
    ]
