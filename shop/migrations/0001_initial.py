# Generated by Django 5.2.1 on 2025-05-28 17:28

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('enabled', models.BooleanField(default=True)),
                ('published', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='ContentCreator',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('image', models.ImageField(blank=True, null=True, upload_to='creators/')),
                ('enabled', models.<PERSON>oleanField(default=True)),
                ('published', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='DownloadLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('platform', models.CharField(choices=[('windows', 'Windows'), ('android', 'Android'), ('mac', 'Mac'), ('linux', 'Linux')], max_length=20, unique=True)),
                ('url', models.URLField(max_length=500)),
                ('enabled', models.BooleanField(default=True)),
                ('published', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='MinecraftServer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('display_name', models.CharField(blank=True, max_length=100, null=True)),
                ('ip', models.GenericIPAddressField()),
                ('port', models.PositiveIntegerField()),
                ('domain', models.CharField(blank=True, max_length=255, null=True)),
                ('rcon_port', models.PositiveIntegerField(blank=True, null=True)),
                ('query_port', models.PositiveIntegerField(blank=True, null=True)),
                ('proxy', models.BooleanField(default=False)),
                ('enabled', models.BooleanField(default=True)),
                ('published', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='Item',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('display_name', models.CharField(blank=True, max_length=150, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='items/')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('ucoin_price', models.PositiveIntegerField(default=0)),
                ('commands', models.TextField(blank=True, help_text="Use ',' to separate multiple commands", null=True)),
                ('expiration_days', models.PositiveIntegerField(blank=True, null=True)),
                ('enabled', models.BooleanField(default=True)),
                ('published', models.BooleanField(default=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='shop.category')),
                ('minecraft_server', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='shop.minecraftserver')),
            ],
        ),
        migrations.CreateModel(
            name='Purchase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('minecraft_username', models.CharField(max_length=100)),
                ('mobile_number', models.CharField(blank=True, max_length=15, null=True)),
                ('state', models.CharField(choices=[('created', 'Created'), ('successful', 'Successful'), ('failed', 'Failed'), ('command_failed', 'Command Failed'), ('finished', 'Finished')], default='created', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('payment_succeeded_at', models.DateTimeField(blank=True, null=True)),
                ('subscription_status', models.CharField(choices=[('onetime', 'Onetime'), ('subscribed', 'Subscribed'), ('expired', 'Expired')], default='onetime', max_length=20)),
                ('authority', models.CharField(blank=True, help_text='Zarinpal authority code', max_length=100, null=True)),
                ('ref_id', models.CharField(blank=True, help_text='Zarinpal reference ID', max_length=100, null=True)),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='shop.item')),
                ('referrer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='shop.contentcreator')),
            ],
        ),
    ]
