# Generated by Django 5.2.1 on 2025-05-29 12:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='category',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='category',
            name='display_name',
            field=models.CharField(blank=True, max_length=150, null=True),
        ),
        migrations.AddField(
            model_name='contentcreator',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='contentcreator',
            name='display_name',
            field=models.CharField(blank=True, max_length=150, null=True),
        ),
        migrations.AddField(
            model_name='item',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='item',
            name='revoke_commands',
            field=models.TextField(blank=True, help_text="Use ',' to separate multiple revoke commands", null=True),
        ),
        migrations.AddField(
            model_name='purchase',
            name='z_response',
            field=models.CharField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='item',
            name='price',
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='purchase',
            name='subscription_status',
            field=models.CharField(choices=[('onetime', 'Onetime'), ('subscribed', 'Subscribed'), ('expired', 'Expired'), ('failed_revoke', 'Failed Revoke')], default='onetime', max_length=20),
        ),
    ]
