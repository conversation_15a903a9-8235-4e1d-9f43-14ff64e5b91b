# Generated by Django 5.2.1 on 2025-07-29 16:51

from django.db import migrations


def create_user_profiles(apps, schema_editor):
    """Create UserProfile instances for existing users"""
    User = apps.get_model('auth', 'User')
    UserProfile = apps.get_model('shop', 'UserProfile')

    for user in User.objects.all():
        UserProfile.objects.get_or_create(user=user)


def reverse_create_user_profiles(apps, schema_editor):
    """Remove all UserProfile instances"""
    UserProfile = apps.get_model('shop', 'UserProfile')
    UserProfile.objects.all().delete()


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0025_add_user_profile'),
    ]

    operations = [
        migrations.RunPython(create_user_profiles, reverse_create_user_profiles),
    ]
