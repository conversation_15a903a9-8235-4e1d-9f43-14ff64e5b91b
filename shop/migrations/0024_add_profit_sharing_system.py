# Generated by Django 5.2.1 on 2025-07-21 01:42

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0023_add_require_user_verification_to_item'),
    ]

    operations = [
        migrations.AddField(
            model_name='contentcreator',
            name='admin_notes',
            field=models.TextField(blank=True, help_text='Internal admin notes', null=True),
        ),
        migrations.AddField(
            model_name='contentcreator',
            name='commission_rate',
            field=models.DecimalField(decimal_places=2, default=20.0, help_text='Commission rate percentage (e.g., 20.00 for 20%)', max_digits=5),
        ),
        migrations.AddField(
            model_name='purchase',
            name='referral_commission',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Calculated profit share amount for the referrer', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='purchase',
            name='referral_settled_at',
            field=models.DateTimeField(blank=True, help_text='When the referral commission was settled', null=True),
        ),
        migrations.AddField(
            model_name='purchase',
            name='referral_settlement_status',
            field=models.CharField(blank=True, choices=[('pending', 'Pending'), ('settled', 'Settled')], help_text='Settlement status for referral commission', max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='purchase',
            name='referrer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='referred_purchases', to='shop.contentcreator'),
        ),
    ]
