# Generated by Django 5.2.1 on 2025-06-10 17:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0012_remove_purchase_item_purchaseitem_purchase_items'),
    ]

    operations = [
        migrations.AddField(
            model_name='purchaseitem',
            name='expires_at',
            field=models.DateTimeField(blank=True, help_text='Expiration time for this specific item', null=True),
        ),
        migrations.AddField(
            model_name='purchaseitem',
            name='subscription_status',
            field=models.CharField(choices=[('onetime', 'Onetime'), ('subscribed', 'Subscribed'), ('expired', 'Expired'), ('failed_revoke', 'Failed Revoke')], default='onetime', help_text='Subscription status for this specific item', max_length=20),
        ),
    ]
