# Generated by Django 5.2.1 on 2025-06-29 12:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0020_change_image_fields_to_url'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='category',
            options={'ordering': ['order', 'name'], 'verbose_name_plural': 'Categories'},
        ),
        migrations.AlterModelOptions(
            name='contentcreator',
            options={'ordering': ['order', 'name']},
        ),
        migrations.AlterModelOptions(
            name='item',
            options={'ordering': ['order', 'name']},
        ),
        migrations.AlterModelOptions(
            name='launcher',
            options={'ordering': ['order', 'name']},
        ),
        migrations.AlterModelOptions(
            name='platform',
            options={'ordering': ['order', 'name']},
        ),
        migrations.AlterModelOptions(
            name='version',
            options={'ordering': ['order', 'name']},
        ),
        migrations.AddField(
            model_name='category',
            name='order',
            field=models.PositiveIntegerField(default=0, help_text='Order for sorting (lower values appear first)'),
        ),
        migrations.AddField(
            model_name='contentcreator',
            name='order',
            field=models.PositiveIntegerField(default=0, help_text='Order for sorting (lower values appear first)'),
        ),
        migrations.AddField(
            model_name='item',
            name='order',
            field=models.PositiveIntegerField(default=0, help_text='Order for sorting (lower values appear first)'),
        ),
        migrations.AddField(
            model_name='launcher',
            name='order',
            field=models.PositiveIntegerField(default=0, help_text='Order for sorting (lower values appear first)'),
        ),
        migrations.AddField(
            model_name='platform',
            name='order',
            field=models.PositiveIntegerField(default=0, help_text='Order for sorting (lower values appear first)'),
        ),
        migrations.AddField(
            model_name='version',
            name='order',
            field=models.PositiveIntegerField(default=0, help_text='Order for sorting (lower values appear first)'),
        ),
    ]
