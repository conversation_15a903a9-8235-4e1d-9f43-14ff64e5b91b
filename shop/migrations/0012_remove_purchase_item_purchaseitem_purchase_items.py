# Generated by Django 5.2.1 on 2025-06-10 16:25

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0011_alter_contentcreator_image_alter_item_image'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='purchase',
            name='item',
        ),
        migrations.CreateModel(
            name='PurchaseItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='shop.item')),
                ('purchase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchase_items', to='shop.purchase')),
            ],
            options={
                'unique_together': {('purchase', 'item')},
            },
        ),
        migrations.AddField(
            model_name='purchase',
            name='items',
            field=models.ManyToManyField(related_name='purchases', through='shop.PurchaseItem', to='shop.item'),
        ),
    ]
