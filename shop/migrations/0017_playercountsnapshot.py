# Generated by Django 5.2.1 on 2025-06-26 23:38

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0016_minecraftserver_rcon_password'),
    ]

    operations = [
        migrations.CreateModel(
            name='PlayerCountSnapshot',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('online_players', models.PositiveIntegerField(help_text='Number of players currently online')),
                ('max_players', models.PositiveIntegerField(help_text='Maximum number of players allowed')),
                ('query_successful', models.BooleanField(default=True, help_text='Whether the server query was successful')),
                ('error_message', models.TextField(blank=True, help_text='Error message if query failed', null=True)),
                ('server', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='player_snapshots', to='shop.minecraftserver')),
            ],
            options={
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['server', '-timestamp'], name='shop_player_server__9aa2c7_idx'), models.Index(fields=['-timestamp'], name='shop_player_timesta_ce3618_idx')],
            },
        ),
    ]
