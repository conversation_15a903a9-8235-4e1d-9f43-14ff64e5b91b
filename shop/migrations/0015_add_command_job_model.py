# Generated by Django 5.2.1 on 2025-06-16 02:36

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0014_remove_purchase_expires_at_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CommandJob',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('command_text', models.TextField(help_text='The actual command to execute')),
                ('sequence_order', models.PositiveIntegerField(help_text='Order in which this command should be executed')),
                ('state', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('successful', 'Successful'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('django_q_task_id', models.CharField(blank=True, help_text='Django-Q task ID', max_length=100, null=True)),
                ('purchase_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='command_jobs', to='shop.purchaseitem')),
            ],
            options={
                'ordering': ['sequence_order'],
                'unique_together': {('purchase_item', 'sequence_order')},
            },
        ),
    ]
