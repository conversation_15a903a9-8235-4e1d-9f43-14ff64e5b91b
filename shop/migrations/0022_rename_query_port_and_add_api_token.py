# Generated by Django 5.2.1 on 2025-07-14 14:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0021_add_order_fields'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='minecraftserver',
            name='query_port',
        ),
        migrations.AddField(
            model_name='minecraftserver',
            name='api_port',
            field=models.PositiveIntegerField(blank=True, help_text='API port for username validation', null=True),
        ),
        migrations.AddField(
            model_name='minecraftserver',
            name='api_token',
            field=models.CharField(blank=True, help_text='API token for username validation', max_length=255, null=True),
        ),
    ]
