from django.urls import path
from .views import PurchaseView, IPGCallbackView, ShopAPIView, HomeAPIView, CheckMinecraftUsernameView, OnlinePlayersAPIView, PurchaseHistoryAPIView

urlpatterns = [
    path('purchase/', PurchaseView.as_view(), name='purchase'),
    path('ipg/callback/', IPGCallbackView.as_view(), name='ipg_callback'),
    path('shop/', ShopAPIView.as_view(), name='shop_api'),
    path('home/', HomeAPIView.as_view(), name='home_api'),
    path('check-username/', CheckMinecraftUsernameView.as_view(), name='check_username'),
    path('online-players/', OnlinePlayersAPIView.as_view(), name='online_players_api'),
    path('purchase-history/', PurchaseHistoryAPIView.as_view(), name='purchase_history_api'),
]
