#!/usr/bin/env python
"""
Test script for the cleaned up multi-item subscription logic
"""
import os
import sys
import django
from datetime import timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.models import *
from django.utils import timezone

def test_cleaned_subscription_logic():
    print("=== Testing Cleaned Multi-Item Subscription Logic ===")
    
    # Get existing items
    items = Item.objects.all()[:2]
    if len(items) < 2:
        print("Not enough items to test with")
        return False
    
    print(f"Testing with items: {[item.name for item in items]}")
    
    # Create a test purchase with multiple items
    purchase = Purchase.objects.create(
        minecraft_username="testuser_cleaned",
        mobile_number="1234567890"
    )
    
    # Add items
    purchase_item1 = PurchaseItem.objects.create(
        purchase=purchase,
        item=items[0],
        quantity=1
    )
    
    purchase_item2 = PurchaseItem.objects.create(
        purchase=purchase,
        item=items[1],
        quantity=2
    )
    
    print(f"\nCreated purchase {purchase.ref_id} with {purchase.purchase_items.count()} items")
    
    # Check what fields exist on Purchase model now
    purchase_fields = [field.name for field in Purchase._meta.get_fields()]
    print(f"Purchase model fields: {purchase_fields}")
    
    # Check what fields exist on PurchaseItem model
    purchase_item_fields = [field.name for field in PurchaseItem._meta.get_fields()]
    print(f"PurchaseItem model fields: {purchase_item_fields}")
    
    # Simulate the cleaned subscription logic
    now = timezone.now()
    
    for purchase_item in purchase.purchase_items.all():
        item = purchase_item.item
        expiration_days = item.expiration_days or 0
        
        if expiration_days > 0:
            # This item has expiration - set up subscription
            new_expires_at = now + timedelta(days=expiration_days)
            
            # Set subscription status for this specific item
            purchase_item.subscription_status = Purchase.SubscriptionStatus.SUBSCRIBED
            purchase_item.expires_at = new_expires_at
            purchase_item.save()
            
            print(f"  ✅ Set up subscription for {item.name} until {new_expires_at}")
            print(f"    Task will be scheduled with unique ID: {purchase_item.id}")
        else:
            # This item is one-time
            purchase_item.subscription_status = Purchase.SubscriptionStatus.ONETIME
            purchase_item.save()
            
            print(f"  ⏱️ Set {item.name} as one-time purchase")
    
    print(f"\n✨ Purchase-level subscription tracking removed - now handled per item!")
    print(f"✨ Old revoke_item task removed - now using revoke_purchase_item!")
    
    # Display final state
    print("\n=== Final Purchase Item States ===")
    for purchase_item in purchase.purchase_items.all():
        print(f"  - {purchase_item.item.name} x{purchase_item.quantity}")
        print(f"    Status: {purchase_item.subscription_status}")
        print(f"    Expires: {purchase_item.expires_at}")
        print(f"    Unique Task ID: {purchase_item.id}")
    
    print("\n=== Cleanup Test Completed Successfully! ===")
    return True

if __name__ == "__main__":
    test_cleaned_subscription_logic()
