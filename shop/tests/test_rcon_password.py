#!/usr/bin/env python
"""
Test script for RCON password functionality
"""
import os
import sys
import django
from unittest.mock import patch, MagicMock

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from django.test import TestCase
from shop.minecraft import send_command_to_minecraft_server
from shop.models import MinecraftServer


class RCONPasswordTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        self.server_with_password = MinecraftServer.objects.create(
            name='test_server_with_password',
            display_name='Test Server With Password',
            ip='127.0.0.1',
            port=25565,
            rcon_port=25575,
            rcon_password='test_password_123',
            enabled=True
        )
        
        self.server_without_password = MinecraftServer.objects.create(
            name='test_server_without_password',
            display_name='Test Server Without Password',
            ip='127.0.0.1',
            port=25566,
            rcon_port=25576,
            rcon_password=None,  # No password set
            enabled=True
        )

    @patch('shop.minecraft.RCONClient')
    @patch('shop.minecraft.config')
    def test_rcon_uses_server_password(self, mock_config, mock_rcon_client):
        """Test that RCON uses the server's password when available"""
        # Setup mocks
        mock_rcon_instance = MagicMock()
        mock_rcon_client.return_value = mock_rcon_instance
        mock_rcon_instance.login.return_value = True
        mock_rcon_instance.command.return_value = "Command executed"
        
        # Call the function
        result = send_command_to_minecraft_server(self.server_with_password, "say hello")
        
        # Verify the server's password was used
        mock_rcon_instance.login.assert_called_once_with('test_password_123')
        # Verify environment config was not called since server has password
        mock_config.assert_not_called()
        self.assertTrue(result)

    @patch('shop.minecraft.RCONClient')
    @patch('shop.minecraft.config')
    def test_rcon_fallback_to_environment(self, mock_config, mock_rcon_client):
        """Test that RCON falls back to environment variable when server has no password"""
        # Setup mocks
        mock_rcon_instance = MagicMock()
        mock_rcon_client.return_value = mock_rcon_instance
        mock_rcon_instance.login.return_value = True
        mock_rcon_instance.command.return_value = "Command executed"
        mock_config.return_value = 'env_password_456'
        
        # Call the function
        result = send_command_to_minecraft_server(self.server_without_password, "say hello")
        
        # Verify environment config was called for fallback
        mock_config.assert_called_once_with("RCON_PASSWORD", default=None)
        # Verify the environment password was used
        mock_rcon_instance.login.assert_called_once_with('env_password_456')
        self.assertTrue(result)

    @patch('shop.minecraft.RCONClient')
    @patch('shop.minecraft.config')
    def test_rcon_fails_when_no_password_available(self, mock_config, mock_rcon_client):
        """Test that RCON fails when neither server nor environment has password"""
        # Setup mocks - no password available
        mock_config.return_value = None
        
        # Call the function
        result = send_command_to_minecraft_server(self.server_without_password, "say hello")
        
        # Verify it failed due to no password
        self.assertFalse(result)
        # Verify RCON client was not even created
        mock_rcon_client.assert_not_called()


if __name__ == "__main__":
    import unittest
    unittest.main()
