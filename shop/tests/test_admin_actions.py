from django.test import TestCase, RequestFactory
from django.contrib.admin.sites import AdminSite
from django.contrib.auth.models import User
from django.contrib import messages
from django.http import HttpRequest
from django.contrib.messages.storage.fallback import FallbackStorage
from unittest.mock import patch, MagicMock
from shop.admin import PurchaseAdmin, PurchaseItemAdmin, CommandJobAdmin
from shop.models import Purchase, PurchaseItem, Item, Category, MinecraftServer, CommandJob


class MockRequest:
    def __init__(self, user):
        self.user = user


class AdminActionsTestCase(TestCase):
    def setUp(self):
        # Create test data
        self.server = MinecraftServer.objects.create(
            name='test_server',
            display_name='Test Server',
            ip='127.0.0.1',
            port=25565
        )
        
        self.category = Category.objects.create(
            name='test_category',
            display_name='Test Category'
        )
        
        self.item = Item.objects.create(
            name='test_item',
            display_name='Test Item',
            category=self.category,
            price=100,
            minecraft_server=self.server,
            commands='give {username} diamond 1'
        )
        
        self.purchase = Purchase.objects.create(
            minecraft_username='testuser',
            state=Purchase.State.COMMAND_FAILED
        )
        
        self.purchase_item = PurchaseItem.objects.create(
            purchase=self.purchase,
            item=self.item,
            quantity=1
        )
        
        # Create failed command jobs
        self.failed_job1 = CommandJob.objects.create(
            purchase_item=self.purchase_item,
            command_text='give testuser diamond 1',
            sequence_order=1,
            state=CommandJob.State.FAILED,
            error_message='Test error'
        )
        
        self.failed_job2 = CommandJob.objects.create(
            purchase_item=self.purchase_item,
            command_text='give testuser emerald 1',
            sequence_order=2,
            state=CommandJob.State.FAILED,
            error_message='Test error 2'
        )
        
        # Create successful command job (should not be affected)
        self.successful_job = CommandJob.objects.create(
            purchase_item=self.purchase_item,
            command_text='give testuser gold 1',
            sequence_order=3,
            state=CommandJob.State.SUCCESSFUL
        )
        
        # Create admin instances
        self.site = AdminSite()
        self.purchase_admin = PurchaseAdmin(Purchase, self.site)
        self.purchase_item_admin = PurchaseItemAdmin(PurchaseItem, self.site)
        self.command_job_admin = CommandJobAdmin(CommandJob, self.site)
        
        # Create mock user and request
        self.user = User.objects.create_user('admin', '<EMAIL>', 'password')
        self.request = MockRequest(self.user)

    @patch('shop.admin.async_task')
    def test_purchase_admin_retry_failed_commands(self, mock_async_task):
        """Test retry failed commands action in PurchaseAdmin"""
        queryset = Purchase.objects.filter(id=self.purchase.id)
        
        # Mock the message_user method
        self.purchase_admin.message_user = MagicMock()
        
        # Execute the action
        self.purchase_admin.retry_failed_commands(self.request, queryset)
        
        # Verify failed jobs were reset to pending
        self.failed_job1.refresh_from_db()
        self.failed_job2.refresh_from_db()
        self.successful_job.refresh_from_db()
        
        self.assertEqual(self.failed_job1.state, CommandJob.State.PENDING)
        self.assertEqual(self.failed_job2.state, CommandJob.State.PENDING)
        self.assertEqual(self.successful_job.state, CommandJob.State.SUCCESSFUL)  # Should not change
        
        # Verify error messages were cleared
        self.assertIsNone(self.failed_job1.error_message)
        self.assertIsNone(self.failed_job2.error_message)
        
        # Verify async task was called
        mock_async_task.assert_called_once_with('shop.tasks.execute_purchase_commands', self.purchase.id)
        
        # Verify success message was shown
        self.purchase_admin.message_user.assert_called()

    @patch('shop.admin.async_task')
    def test_purchase_item_admin_retry_failed_commands(self, mock_async_task):
        """Test retry failed commands action in PurchaseItemAdmin"""
        queryset = PurchaseItem.objects.filter(id=self.purchase_item.id)
        
        # Mock the message_user method
        self.purchase_item_admin.message_user = MagicMock()
        
        # Execute the action
        self.purchase_item_admin.retry_failed_commands(self.request, queryset)
        
        # Verify failed jobs were reset to pending
        self.failed_job1.refresh_from_db()
        self.failed_job2.refresh_from_db()
        
        self.assertEqual(self.failed_job1.state, CommandJob.State.PENDING)
        self.assertEqual(self.failed_job2.state, CommandJob.State.PENDING)
        
        # Verify async task was called
        mock_async_task.assert_called_once_with('shop.tasks.execute_purchase_item_commands', self.purchase_item.id)

    @patch('shop.admin.async_task')
    def test_command_job_admin_retry_failed_commands(self, mock_async_task):
        """Test retry failed commands action in CommandJobAdmin"""
        queryset = CommandJob.objects.filter(id__in=[self.failed_job1.id, self.successful_job.id])

        # Mock the message_user method
        self.command_job_admin.message_user = MagicMock()

        # Execute the action
        self.command_job_admin.retry_failed_commands(self.request, queryset)

        # Verify only failed job was reset to pending
        self.failed_job1.refresh_from_db()
        self.successful_job.refresh_from_db()

        self.assertEqual(self.failed_job1.state, CommandJob.State.PENDING)
        self.assertEqual(self.successful_job.state, CommandJob.State.SUCCESSFUL)  # Should not change

        # Verify async task was called only for the failed job
        mock_async_task.assert_called_with('shop.tasks.execute_single_command', self.failed_job1.id)
        self.assertEqual(mock_async_task.call_count, 1)

    @patch('shop.admin.async_task')
    def test_purchase_admin_no_failed_commands(self, mock_async_task):
        """Test retry action when purchase has no failed commands"""
        # Create a purchase with only successful commands
        purchase2 = Purchase.objects.create(
            minecraft_username='testuser2',
            state=Purchase.State.FINISHED
        )
        
        queryset = Purchase.objects.filter(id=purchase2.id)
        
        # Mock the message_user method
        self.purchase_admin.message_user = MagicMock()
        
        # Execute the action
        self.purchase_admin.retry_failed_commands(self.request, queryset)
        
        # Verify async task was not called
        mock_async_task.assert_not_called()
        
        # Verify warning message was shown
        self.purchase_admin.message_user.assert_called()

    @patch('shop.admin.async_task')
    def test_command_job_admin_no_duplicate_messages(self, mock_async_task):
        """Test that we don't get both success and warning messages when retrying failed jobs"""
        # Create a queryset with both failed and successful jobs
        queryset = CommandJob.objects.filter(id__in=[self.failed_job1.id, self.successful_job.id])

        # Mock the message_user method
        self.command_job_admin.message_user = MagicMock()

        # Execute the action
        self.command_job_admin.retry_failed_commands(self.request, queryset)

        # Verify message_user was called exactly twice (once for success, once for warning)
        self.assertEqual(self.command_job_admin.message_user.call_count, 2)

        # Verify the messages
        calls = self.command_job_admin.message_user.call_args_list
        success_call = calls[0]
        warning_call = calls[1]

        # Check success message
        self.assertIn('Successfully queued 1 failed command job(s)', success_call[0][1])
        self.assertEqual(success_call[0][2], messages.SUCCESS)

        # Check warning message
        self.assertIn('Skipped 1 command job(s) that are not in failed state', warning_call[0][1])
        self.assertEqual(warning_call[0][2], messages.WARNING)

    @patch('shop.admin.async_task')
    def test_command_job_admin_retry_only_failed_commands(self, mock_async_task):
        """Test retry action with only failed command jobs"""
        queryset = CommandJob.objects.filter(state=CommandJob.State.FAILED)

        # Mock the message_user method
        self.command_job_admin.message_user = MagicMock()

        # Execute the action
        self.command_job_admin.retry_failed_commands(self.request, queryset)

        # Verify failed jobs were reset to pending
        self.failed_job1.refresh_from_db()
        self.failed_job2.refresh_from_db()

        self.assertEqual(self.failed_job1.state, CommandJob.State.PENDING)
        self.assertEqual(self.failed_job2.state, CommandJob.State.PENDING)

        # Verify async task was called for both failed jobs
        self.assertEqual(mock_async_task.call_count, 2)
        mock_async_task.assert_any_call('shop.tasks.execute_single_command', self.failed_job1.id)
        mock_async_task.assert_any_call('shop.tasks.execute_single_command', self.failed_job2.id)


class RunPendingOrdersTestCase(TestCase):
    def setUp(self):
        """Set up test data for run_pending_orders tests"""
        # Create test server
        self.server = MinecraftServer.objects.create(
            name="test_server",
            display_name="Test Server",
            ip="127.0.0.1",
            port=25565,
            rcon_port=25575,
            api_port=28535,
            api_token="test_token",
            enabled=True,
            published=True
        )

        # Create test category
        self.category = Category.objects.create(
            name="test_category",
            display_name="Test Category",
            enabled=True,
            published=True
        )

        # Create test items
        self.item_with_verification = Item.objects.create(
            name="verified_item",
            display_name="Verified Item",
            category=self.category,
            price=100,
            commands="give {username} diamond 1",
            minecraft_server=self.server,
            require_user_verification=True,
            enabled=True,
            published=True
        )

        self.item_without_verification = Item.objects.create(
            name="unverified_item",
            display_name="Unverified Item",
            category=self.category,
            price=50,
            commands="give {username} gold_ingot 1",
            minecraft_server=self.server,
            require_user_verification=False,
            enabled=True,
            published=True
        )

        # Create test purchases
        self.purchase_1 = Purchase.objects.create(
            minecraft_username="testuser1",
            state=Purchase.State.SUCCESSFUL
        )

        self.purchase_2 = Purchase.objects.create(
            minecraft_username="testuser2",
            state=Purchase.State.SUCCESSFUL
        )

        # Create purchase items
        self.purchase_item_verified = PurchaseItem.objects.create(
            purchase=self.purchase_1,
            item=self.item_with_verification,
            quantity=1
        )

        self.purchase_item_unverified = PurchaseItem.objects.create(
            purchase=self.purchase_2,
            item=self.item_without_verification,
            quantity=1
        )

        # Create command jobs
        self.command_job_verified = CommandJob.objects.create(
            purchase_item=self.purchase_item_verified,
            command_text="give testuser1 diamond 1",
            sequence_order=1,
            state=CommandJob.State.PENDING
        )

        self.command_job_unverified = CommandJob.objects.create(
            purchase_item=self.purchase_item_unverified,
            command_text="give testuser2 gold_ingot 1",
            sequence_order=1,
            state=CommandJob.State.PENDING
        )

        # Set up admin and request
        self.factory = RequestFactory()
        self.user = User.objects.create_superuser('admin', '<EMAIL>', 'password')
        self.site = AdminSite()

        # Initialize admin classes
        self.purchase_admin = PurchaseAdmin(Purchase, self.site)
        self.purchase_item_admin = PurchaseItemAdmin(PurchaseItem, self.site)
        self.command_job_admin = CommandJobAdmin(CommandJob, self.site)

    def _create_request(self):
        """Create a mock request with messages support"""
        request = self.factory.post('/')
        request.user = self.user
        # Add messages framework support
        setattr(request, 'session', {})
        messages = FallbackStorage(request)
        setattr(request, '_messages', messages)
        return request

    @patch('shop.admin.is_user_present_on_server')
    @patch('shop.admin.async_task')
    def test_purchase_admin_run_pending_orders_user_present(self, mock_async_task, mock_user_present):
        """Test PurchaseAdmin run_pending_orders action when user is present"""
        mock_user_present.return_value = True

        request = self._create_request()
        queryset = Purchase.objects.filter(id=self.purchase_1.id)

        self.purchase_admin.run_pending_orders(request, queryset)

        # Verify user verification was checked
        mock_user_present.assert_called_once_with("testuser1", self.server)

        # Verify task was queued
        mock_async_task.assert_called_once_with('shop.tasks.execute_purchase_commands', self.purchase_1.id)

    @patch('shop.admin.is_user_present_on_server')
    @patch('shop.admin.async_task')
    def test_purchase_admin_run_pending_orders_user_not_present(self, mock_async_task, mock_user_present):
        """Test PurchaseAdmin run_pending_orders action when user is not present"""
        mock_user_present.return_value = False

        request = self._create_request()
        queryset = Purchase.objects.filter(id=self.purchase_1.id)

        self.purchase_admin.run_pending_orders(request, queryset)

        # Verify user verification was checked
        mock_user_present.assert_called_once_with("testuser1", self.server)

        # Verify task was NOT queued
        mock_async_task.assert_not_called()

    @patch('shop.admin.async_task')
    def test_purchase_admin_run_pending_orders_no_verification_required(self, mock_async_task):
        """Test PurchaseAdmin run_pending_orders action when no verification is required"""
        request = self._create_request()
        queryset = Purchase.objects.filter(id=self.purchase_2.id)

        self.purchase_admin.run_pending_orders(request, queryset)

        # Verify task was queued without verification
        mock_async_task.assert_called_once_with('shop.tasks.execute_purchase_commands', self.purchase_2.id)

    @patch('shop.admin.is_user_present_on_server')
    @patch('shop.admin.async_task')
    def test_purchase_item_admin_run_pending_orders_user_present(self, mock_async_task, mock_user_present):
        """Test PurchaseItemAdmin run_pending_orders action when user is present"""
        mock_user_present.return_value = True

        request = self._create_request()
        queryset = PurchaseItem.objects.filter(id=self.purchase_item_verified.id)

        self.purchase_item_admin.run_pending_orders(request, queryset)

        # Verify user verification was checked
        mock_user_present.assert_called_once_with("testuser1", self.server)

        # Verify task was queued
        mock_async_task.assert_called_once_with('shop.tasks.execute_purchase_item_commands', self.purchase_item_verified.id)

    @patch('shop.admin.is_user_present_on_server')
    @patch('shop.admin.async_task')
    def test_command_job_admin_run_pending_orders_user_present(self, mock_async_task, mock_user_present):
        """Test CommandJobAdmin run_pending_orders action when user is present"""
        mock_user_present.return_value = True

        request = self._create_request()
        queryset = CommandJob.objects.filter(id=self.command_job_verified.id)

        self.command_job_admin.run_pending_orders(request, queryset)

        # Verify user verification was checked
        mock_user_present.assert_called_once_with("testuser1", self.server)

        # Verify task was queued
        mock_async_task.assert_called_once_with('shop.tasks.execute_single_command', self.command_job_verified.id)

    @patch('shop.admin.is_user_present_on_server')
    @patch('shop.admin.async_task')
    def test_command_job_admin_run_pending_orders_user_not_present(self, mock_async_task, mock_user_present):
        """Test CommandJobAdmin run_pending_orders action when user is not present"""
        mock_user_present.return_value = False

        request = self._create_request()
        queryset = CommandJob.objects.filter(id=self.command_job_verified.id)

        self.command_job_admin.run_pending_orders(request, queryset)

        # Verify user verification was checked
        mock_user_present.assert_called_once_with("testuser1", self.server)

        # Verify task was NOT queued
        mock_async_task.assert_not_called()

    def test_purchase_admin_run_pending_orders_no_pending_commands(self):
        """Test PurchaseAdmin run_pending_orders action when no pending commands exist"""
        # Mark command as successful
        self.command_job_verified.state = CommandJob.State.SUCCESSFUL
        self.command_job_verified.save()

        request = self._create_request()
        queryset = Purchase.objects.filter(id=self.purchase_1.id)

        with patch('shop.admin.async_task') as mock_async_task:
            self.purchase_admin.run_pending_orders(request, queryset)

            # Verify no task was queued
            mock_async_task.assert_not_called()
