#!/usr/bin/env python
"""
Test script for RCON functionality using mctools
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.minecraft import send_command_to_minecraft_server
from shop.models import MinecraftServer

def test_rcon_connection():
    """Test RCON connection and command execution"""
    
    # Get a server with RCON configuration
    server = MinecraftServer.objects.filter(
        rcon_port__isnull=False, 
        enabled=True
    ).first()
    
    if not server:
        print("No server with RCON configuration found")
        return False
    
    print(f"Testing RCON connection to server: {server.name} ({server.ip}:{server.rcon_port})")
    
    # Test with a simple command (list players)
    test_command = "say hello rc"
    
    success = send_command_to_minecraft_server(server, test_command)
    
    if success:
        print("✅ RCON test successful!")
    else:
        print("❌ RCON test failed!")
    
    return success

if __name__ == "__main__":
    print("Testing RCON functionality with mctools...")
    print("Make sure you have RCON_PASSWORD set in your .env file")
    print("-" * 50)
    
    test_rcon_connection()
