from decimal import Decimal
from django.test import TestCase
from django.utils import timezone
from shop.models import Purchase, PurchaseItem, Item, Category, ContentCreator, MinecraftServer


class ProfitSharingTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create a content creator
        self.content_creator = ContentCreator.objects.create(
            name="TestCreator",
            display_name="Test Creator",
            commission_rate=Decimal('25.00'),  # 25% commission
            enabled=True,
            published=True
        )
        
        # Create a category
        self.category = Category.objects.create(
            name="TestCategory",
            enabled=True,
            published=True
        )
        
        # Create a minecraft server
        self.server = MinecraftServer.objects.create(
            name="TestServer",
            ip="127.0.0.1",
            port=25565,
            enabled=True,
            published=True
        )
        
        # Create test items
        self.item1 = Item.objects.create(
            name="TestItem1",
            category=self.category,
            price=1000,  # 10.00 in currency
            minecraft_server=self.server,
            enabled=True,
            published=True
        )
        
        self.item2 = Item.objects.create(
            name="TestItem2",
            category=self.category,
            price=2000,  # 20.00 in currency
            minecraft_server=self.server,
            enabled=True,
            published=True
        )

    def test_commission_calculation_single_item(self):
        """Test commission calculation for a single item purchase"""
        # Create a purchase with referrer
        purchase = Purchase.objects.create(
            minecraft_username="testuser",
            referrer=self.content_creator
        )
        
        # Add item to purchase
        PurchaseItem.objects.create(
            purchase=purchase,
            item=self.item1,
            quantity=1
        )
        
        # Simulate payment success
        purchase.payment_succeeded_at = timezone.now()
        purchase.save()

        # Calculate commission (manual call approach)
        purchase.calculate_and_set_referral_commission()

        # Check commission calculation
        expected_commission = Decimal('1000') * Decimal('25.00') / Decimal('100')  # 250.00
        self.assertEqual(purchase.referral_commission, expected_commission)
        self.assertEqual(purchase.referral_settlement_status, Purchase.ReferralSettlementStatus.PENDING)
        self.assertIsNone(purchase.referral_settled_at)

    def test_commission_calculation_multiple_items(self):
        """Test commission calculation for multiple items purchase"""
        # Create a purchase with referrer
        purchase = Purchase.objects.create(
            minecraft_username="testuser",
            referrer=self.content_creator
        )
        
        # Add items to purchase
        PurchaseItem.objects.create(
            purchase=purchase,
            item=self.item1,
            quantity=2  # 2 x 1000 = 2000
        )
        PurchaseItem.objects.create(
            purchase=purchase,
            item=self.item2,
            quantity=1  # 1 x 2000 = 2000
        )
        
        # Simulate payment success
        purchase.payment_succeeded_at = timezone.now()
        purchase.save()

        # Calculate commission (manual call approach)
        purchase.calculate_and_set_referral_commission()

        # Total: 4000, Commission: 4000 * 25% = 1000
        expected_commission = Decimal('4000') * Decimal('25.00') / Decimal('100')  # 1000.00
        self.assertEqual(purchase.referral_commission, expected_commission)
        self.assertEqual(purchase.referral_settlement_status, Purchase.ReferralSettlementStatus.PENDING)

    def test_no_commission_without_referrer(self):
        """Test that no commission is calculated without referrer"""
        # Create a purchase without referrer
        purchase = Purchase.objects.create(
            minecraft_username="testuser"
        )
        
        # Add item to purchase
        PurchaseItem.objects.create(
            purchase=purchase,
            item=self.item1,
            quantity=1
        )
        
        # Simulate payment success
        purchase.payment_succeeded_at = timezone.now()
        purchase.save()

        # Try to calculate commission (should do nothing without referrer)
        purchase.calculate_and_set_referral_commission()

        # Check no commission is calculated
        self.assertIsNone(purchase.referral_commission)
        self.assertIsNone(purchase.referral_settlement_status)
        self.assertIsNone(purchase.referral_settled_at)

    def test_no_commission_without_payment_success(self):
        """Test that no commission is calculated without payment success"""
        # Create a purchase with referrer but no payment success
        purchase = Purchase.objects.create(
            minecraft_username="testuser",
            referrer=self.content_creator
        )
        
        # Add item to purchase
        PurchaseItem.objects.create(
            purchase=purchase,
            item=self.item1,
            quantity=1
        )
        
        # Save without setting payment_succeeded_at
        purchase.save()
        
        # Check no commission is calculated
        self.assertIsNone(purchase.referral_commission)
        self.assertIsNone(purchase.referral_settlement_status)
        self.assertIsNone(purchase.referral_settled_at)

    def test_content_creator_earnings_properties(self):
        """Test ContentCreator earnings calculation properties"""
        # Create multiple purchases with different settlement statuses
        
        # Purchase 1: Pending settlement
        purchase1 = Purchase.objects.create(
            minecraft_username="testuser1",
            referrer=self.content_creator,
            payment_succeeded_at=timezone.now(),
            referral_commission=Decimal('250.00'),
            referral_settlement_status=Purchase.ReferralSettlementStatus.PENDING
        )
        PurchaseItem.objects.create(purchase=purchase1, item=self.item1, quantity=1)
        
        # Purchase 2: Settled
        purchase2 = Purchase.objects.create(
            minecraft_username="testuser2",
            referrer=self.content_creator,
            payment_succeeded_at=timezone.now(),
            referral_commission=Decimal('500.00'),
            referral_settlement_status=Purchase.ReferralSettlementStatus.SETTLED,
            referral_settled_at=timezone.now()
        )
        PurchaseItem.objects.create(purchase=purchase2, item=self.item2, quantity=1)
        
        # Purchase 3: Another pending
        purchase3 = Purchase.objects.create(
            minecraft_username="testuser3",
            referrer=self.content_creator,
            payment_succeeded_at=timezone.now(),
            referral_commission=Decimal('100.00'),
            referral_settlement_status=Purchase.ReferralSettlementStatus.PENDING
        )
        PurchaseItem.objects.create(purchase=purchase3, item=self.item1, quantity=1)
        
        # Test earnings calculations
        self.assertEqual(self.content_creator.total_earned, Decimal('850.00'))  # 250 + 500 + 100
        self.assertEqual(self.content_creator.total_settled, Decimal('500.00'))  # Only purchase2
        self.assertEqual(self.content_creator.total_unsettled, Decimal('350.00'))  # 250 + 100

    def test_commission_not_recalculated_if_already_set(self):
        """Test that commission is not recalculated if already set"""
        # Create a purchase with referrer
        purchase = Purchase.objects.create(
            minecraft_username="testuser",
            referrer=self.content_creator,
            payment_succeeded_at=timezone.now(),
            referral_commission=Decimal('999.99')  # Manually set commission
        )
        
        # Add item to purchase
        PurchaseItem.objects.create(
            purchase=purchase,
            item=self.item1,
            quantity=1
        )
        
        # Save again - should not recalculate
        purchase.save()
        
        # Commission should remain the manually set value
        self.assertEqual(purchase.referral_commission, Decimal('999.99'))

    def test_recalculate_commission_with_force(self):
        """Test recalculating commission with force=True"""
        # Create a purchase with referrer and existing commission
        purchase = Purchase.objects.create(
            minecraft_username="testuser",
            referrer=self.content_creator,
            payment_succeeded_at=timezone.now(),
            referral_commission=Decimal('999.99'),  # Manually set commission
            referral_settlement_status=Purchase.ReferralSettlementStatus.SETTLED,
            referral_settled_at=timezone.now()
        )

        # Add item to purchase
        PurchaseItem.objects.create(
            purchase=purchase,
            item=self.item1,
            quantity=1
        )

        # Recalculate with force=True
        purchase.recalculate_referral_commission(force=True)
        purchase.save()

        # Commission should be recalculated and status reset to pending
        expected_commission = Decimal('1000') * Decimal('25.00') / Decimal('100')  # 250.00
        self.assertEqual(purchase.referral_commission, expected_commission)
        self.assertEqual(purchase.referral_settlement_status, Purchase.ReferralSettlementStatus.PENDING)
        self.assertIsNone(purchase.referral_settled_at)
