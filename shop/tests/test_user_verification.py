#!/usr/bin/env python
"""
Test cases for user verification functionality
"""
import os
import sys
import django
from unittest.mock import patch, Mock

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from django.test import TestCase
from shop.models import MinecraftServer, Category, Item, Purchase, PurchaseItem, CommandJob
from shop.minecraft import is_user_present_on_server
from shop.tasks import execute_purchase_item_commands


class UserVerificationTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create test server
        self.server = MinecraftServer.objects.create(
            name="test_server",
            display_name="Test Server",
            ip="127.0.0.1",
            port=25565,
            rcon_port=25575,
            api_port=28535,
            api_token="test_token",
            enabled=True,
            published=True
        )
        
        # Create test category
        self.category = Category.objects.create(
            name="test_category",
            display_name="Test Category",
            enabled=True,
            published=True
        )
        
        # Create test item with user verification required
        self.item_with_verification = Item.objects.create(
            name="verified_item",
            display_name="Verified Item",
            description="An item that requires user verification",
            category=self.category,
            price=100,
            commands="give {username} diamond 1",
            minecraft_server=self.server,
            require_user_verification=True,
            enabled=True,
            published=True
        )
        
        # Create test item without user verification
        self.item_without_verification = Item.objects.create(
            name="unverified_item",
            display_name="Unverified Item",
            description="An item that doesn't require user verification",
            category=self.category,
            price=50,
            commands="give {username} gold_ingot 1",
            minecraft_server=self.server,
            require_user_verification=False,
            enabled=True,
            published=True
        )
        
        # Create test purchase
        self.purchase = Purchase.objects.create(
            minecraft_username="testuser",
            state=Purchase.State.SUCCESSFUL
        )

    def test_is_user_present_on_server_success(self):
        """Test successful user verification"""
        mock_response = Mock()
        mock_response.json.return_value = {
            "success": True,
            "user": "testuser",
            "hasPlayedBefore": True
        }
        mock_response.raise_for_status.return_value = None
        
        with patch('shop.minecraft.requests.post', return_value=mock_response):
            result = is_user_present_on_server("testuser", self.server)
            self.assertTrue(result)

    def test_is_user_present_on_server_user_not_found(self):
        """Test user verification when user hasn't played before"""
        mock_response = Mock()
        mock_response.json.return_value = {
            "success": True,
            "user": "newuser",
            "hasPlayedBefore": False
        }
        mock_response.raise_for_status.return_value = None
        
        with patch('shop.minecraft.requests.post', return_value=mock_response):
            result = is_user_present_on_server("newuser", self.server)
            self.assertFalse(result)

    def test_is_user_present_on_server_no_api_port(self):
        """Test user verification when server has no API port"""
        server_no_api = MinecraftServer.objects.create(
            name="no_api_server",
            ip="127.0.0.1",
            port=25565,
            api_port=None,
            enabled=True
        )
        
        result = is_user_present_on_server("testuser", server_no_api)
        self.assertFalse(result)

    @patch('shop.tasks.is_user_present_on_server')
    @patch('shop.tasks.execute_single_command')
    def test_execute_commands_with_verification_user_present(self, mock_execute_command, mock_user_present):
        """Test command execution when user verification is required and user is present"""
        mock_user_present.return_value = True
        mock_execute_command.return_value = True
        
        # Create purchase item
        purchase_item = PurchaseItem.objects.create(
            purchase=self.purchase,
            item=self.item_with_verification,
            quantity=1
        )
        
        # Create command job
        CommandJob.objects.create(
            purchase_item=purchase_item,
            command_text="give testuser diamond 1",
            sequence_order=1,
            state=CommandJob.State.PENDING
        )
        
        # Execute commands
        execute_purchase_item_commands(purchase_item.id)
        
        # Verify user presence was checked
        mock_user_present.assert_called_once_with("testuser", self.server)
        
        # Verify command was executed
        mock_execute_command.assert_called_once()

    @patch('shop.tasks.is_user_present_on_server')
    @patch('shop.tasks.execute_single_command')
    def test_execute_commands_with_verification_user_not_present(self, mock_execute_command, mock_user_present):
        """Test command execution when user verification is required and user is not present"""
        mock_user_present.return_value = False
        
        # Create purchase item
        purchase_item = PurchaseItem.objects.create(
            purchase=self.purchase,
            item=self.item_with_verification,
            quantity=1
        )
        
        # Create command job
        command_job = CommandJob.objects.create(
            purchase_item=purchase_item,
            command_text="give testuser diamond 1",
            sequence_order=1,
            state=CommandJob.State.PENDING
        )
        
        # Execute commands
        execute_purchase_item_commands(purchase_item.id)
        
        # Verify user presence was checked
        mock_user_present.assert_called_once_with("testuser", self.server)
        
        # Verify command was NOT executed
        mock_execute_command.assert_not_called()
        
        # Verify command job remains in PENDING state
        command_job.refresh_from_db()
        self.assertEqual(command_job.state, CommandJob.State.PENDING)

    @patch('shop.tasks.execute_single_command')
    def test_execute_commands_without_verification(self, mock_execute_command):
        """Test command execution when user verification is not required"""
        mock_execute_command.return_value = True
        
        # Create purchase item
        purchase_item = PurchaseItem.objects.create(
            purchase=self.purchase,
            item=self.item_without_verification,
            quantity=1
        )
        
        # Create command job
        CommandJob.objects.create(
            purchase_item=purchase_item,
            command_text="give testuser gold_ingot 1",
            sequence_order=1,
            state=CommandJob.State.PENDING
        )
        
        # Execute commands
        execute_purchase_item_commands(purchase_item.id)
        
        # Verify command was executed without verification
        mock_execute_command.assert_called_once()


if __name__ == '__main__':
    import unittest
    unittest.main()
