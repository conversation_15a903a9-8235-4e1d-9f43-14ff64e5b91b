#!/usr/bin/env python
"""
Test script for the new multi-item subscription logic
"""
import os
import sys
import django
from datetime import timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.models import *
from django.utils import timezone

def test_subscription_logic():
    print("=== Testing Multi-Item Subscription Logic ===")
    
    # Get existing items
    items = Item.objects.all()[:3]
    if len(items) < 2:
        print("Not enough items to test with")
        return False
    
    print(f"Testing with items: {[item.name for item in items]}")
    
    # Create a test purchase with multiple items
    purchase = Purchase.objects.create(
        minecraft_username="testuser_subscription",
        mobile_number="1234567890"
    )
    
    # Add items with different expiration settings
    purchase_item1 = PurchaseItem.objects.create(
        purchase=purchase,
        item=items[0],  # This should have expiration_days
        quantity=1
    )
    
    purchase_item2 = PurchaseItem.objects.create(
        purchase=purchase,
        item=items[1],  # This might have different expiration
        quantity=2
    )
    
    print(f"\nCreated purchase {purchase.ref_id} with {purchase.purchase_items.count()} items")
    
    # Check item expiration settings
    for purchase_item in purchase.purchase_items.all():
        item = purchase_item.item
        expiration_days = item.expiration_days or 0
        print(f"  - {item.name}: {expiration_days} days expiration")
    
    # Simulate the subscription logic from the view (cleaned up version)
    now = timezone.now()

    for purchase_item in purchase.purchase_items.all():
        item = purchase_item.item
        expiration_days = item.expiration_days or 0

        if expiration_days > 0:
            # This item has expiration - set up subscription
            new_expires_at = now + timedelta(days=expiration_days)

            # Set subscription status for this specific item
            purchase_item.subscription_status = Purchase.SubscriptionStatus.SUBSCRIBED
            purchase_item.expires_at = new_expires_at
            purchase_item.save()

            print(f"  ✅ Set up subscription for {item.name} until {new_expires_at}")
        else:
            # This item is one-time
            purchase_item.subscription_status = Purchase.SubscriptionStatus.ONETIME
            purchase_item.save()

            print(f"  ⏱️ Set {item.name} as one-time purchase")

    print(f"\n✨ Purchase-level subscription tracking removed - now handled per item!")
    
    # Display final state
    print("\n=== Final Purchase Item States ===")
    for purchase_item in purchase.purchase_items.all():
        print(f"  - {purchase_item.item.name} x{purchase_item.quantity}")
        print(f"    Status: {purchase_item.subscription_status}")
        print(f"    Expires: {purchase_item.expires_at}")
        print(f"    Unique ID for tasks: {purchase_item.id}")
    
    print("\n=== Test completed successfully! ===")
    return True

if __name__ == "__main__":
    test_subscription_logic()
