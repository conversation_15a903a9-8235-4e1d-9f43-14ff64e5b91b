#!/usr/bin/env python
"""
Test script for the multi-item Purchase API
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.models import *
import json

def test_multi_item_api():
    print("=== Testing Multi-Item Purchase API ===")
    
    # Check current state
    print(f"Items in database: {Item.objects.count()}")
    print(f"Purchases in database: {Purchase.objects.count()}")
    print(f"PurchaseItems in database: {PurchaseItem.objects.count()}")
    
    # Create test data if needed
    if Item.objects.count() == 0:
        print("\nCreating test data...")
        
        # Create a minecraft server
        server = MinecraftServer.objects.create(
            name="test_server",
            display_name="Test Server",
            ip="127.0.0.1",
            port=25565,
            enabled=True,
            published=True
        )
        
        # Create a category
        category = Category.objects.create(
            name="test_category",
            display_name="Test Category",
            enabled=True,
            published=True
        )
        
        # Create test items
        item1 = Item.objects.create(
            name="test_item_1",
            display_name="Test Item 1",
            description="A test item",
            category=category,
            price=100,
            commands="give {username} diamond 1",
            minecraft_server=server,
            enabled=True,
            published=True
        )
        
        item2 = Item.objects.create(
            name="test_item_2",
            display_name="Test Item 2",
            description="Another test item",
            category=category,
            price=200,
            commands="give {username} gold_ingot 5",
            minecraft_server=server,
            enabled=True,
            published=True
        )
        
        print(f"Created {Item.objects.count()} test items")
    
    # Test creating a purchase with multiple items
    print("\n=== Testing Purchase Creation ===")
    
    items = Item.objects.all()[:2]
    if len(items) >= 2:
        # Create a purchase
        purchase = Purchase.objects.create(
            minecraft_username="testuser",
            mobile_number="1234567890"
        )
        
        # Add items to purchase
        PurchaseItem.objects.create(
            purchase=purchase,
            item=items[0],
            quantity=2
        )
        
        PurchaseItem.objects.create(
            purchase=purchase,
            item=items[1],
            quantity=1
        )
        
        print(f"Created purchase {purchase.ref_id}")
        print(f"Purchase items: {purchase.purchase_items.count()}")
        print(f"Total price: {purchase.get_total_price()}")
        print(f"Items description: {purchase.get_items_description()}")
        
        # Test the __str__ method
        print(f"Purchase string representation: {purchase}")
        
        # Test getting all items
        all_items = purchase.purchase_items.all()
        for purchase_item in all_items:
            print(f"  - {purchase_item.item.name} x{purchase_item.quantity} = {purchase_item.item.price * purchase_item.quantity}")
        
        print("\n=== Test completed successfully! ===")
        return True
    else:
        print("Not enough items to test with")
        return False

if __name__ == "__main__":
    test_multi_item_api()
