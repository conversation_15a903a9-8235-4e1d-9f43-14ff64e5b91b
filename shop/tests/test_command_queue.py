from django.test import TestCase
from django.utils import timezone
from shop.models import Purchase, PurchaseItem, Item, Category, MinecraftServer, CommandJob
from shop.tasks import create_command_jobs_for_purchase, execute_single_command
from shop.utils import replace_command_placeholders
from unittest.mock import patch
import re


class CommandQueueTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create test server
        self.server = MinecraftServer.objects.create(
            name="test_server",
            display_name="Test Server",
            ip="127.0.0.1",
            port=25565,
            rcon_port=25575,
            enabled=True,
            published=True
        )
        
        # Create test category
        self.category = Category.objects.create(
            name="test_category",
            display_name="Test Category",
            enabled=True,
            published=True
        )
        
        # Create test item with commands
        self.item = Item.objects.create(
            name="test_item",
            display_name="Test Item",
            description="A test item",
            category=self.category,
            price=100,
            commands="give {username} diamond 1,give {username} gold_ingot 5",
            minecraft_server=self.server,
            enabled=True,
            published=True
        )
        
        # Create test purchase
        self.purchase = Purchase.objects.create(
            minecraft_username="testuser",
            mobile_number="1234567890",
            state=Purchase.State.SUCCESSFUL
        )
        
        # Create purchase item
        self.purchase_item = PurchaseItem.objects.create(
            purchase=self.purchase,
            item=self.item,
            quantity=2
        )

    def test_create_command_jobs_for_purchase(self):
        """Test that command jobs are created correctly"""
        # Initially no command jobs should exist
        self.assertEqual(CommandJob.objects.count(), 0)
        
        # Create command jobs for the purchase
        create_command_jobs_for_purchase(self.purchase.id)
        
        # Should create 4 jobs (2 commands × 2 quantity)
        self.assertEqual(CommandJob.objects.count(), 4)
        
        # Check that jobs are created with correct sequence order
        jobs = CommandJob.objects.filter(purchase_item=self.purchase_item).order_by('sequence_order')
        
        expected_commands = [
            "give testuser diamond 1",
            "give testuser gold_ingot 5",
            "give testuser diamond 1",
            "give testuser gold_ingot 5"
        ]
        
        for i, job in enumerate(jobs):
            self.assertEqual(job.command_text, expected_commands[i])
            self.assertEqual(job.sequence_order, i + 1)
            self.assertEqual(job.state, CommandJob.State.PENDING)

    def test_purchase_item_command_execution_state(self):
        """Test that purchase item correctly reports command execution state"""
        # Initially should be pending (no jobs)
        self.assertEqual(self.purchase_item.get_command_execution_state(), 'pending')
        
        # Create some command jobs
        job1 = CommandJob.objects.create(
            purchase_item=self.purchase_item,
            command_text="give testuser diamond 1",
            sequence_order=1,
            state=CommandJob.State.PENDING
        )
        
        job2 = CommandJob.objects.create(
            purchase_item=self.purchase_item,
            command_text="give testuser gold_ingot 5",
            sequence_order=2,
            state=CommandJob.State.PENDING
        )
        
        # Should still be pending
        self.assertEqual(self.purchase_item.get_command_execution_state(), 'pending')
        
        # Mark one as running
        job1.state = CommandJob.State.RUNNING
        job1.save()
        self.assertEqual(self.purchase_item.get_command_execution_state(), 'running')
        
        # Mark one as failed
        job1.state = CommandJob.State.FAILED
        job1.save()
        self.assertEqual(self.purchase_item.get_command_execution_state(), 'failed')
        
        # Mark both as successful
        job1.state = CommandJob.State.SUCCESSFUL
        job1.save()
        job2.state = CommandJob.State.SUCCESSFUL
        job2.save()
        self.assertEqual(self.purchase_item.get_command_execution_state(), 'successful')

    def test_purchase_derived_state_from_commands(self):
        """Test that purchase correctly derives state from command execution"""
        # Initially should return current state
        self.assertEqual(self.purchase.get_derived_state_from_commands(), Purchase.State.SUCCESSFUL)
        
        # Create command jobs for testing
        job1 = CommandJob.objects.create(
            purchase_item=self.purchase_item,
            command_text="give testuser diamond 1",
            sequence_order=1,
            state=CommandJob.State.PENDING
        )
        
        # With pending commands, should remain SUCCESSFUL
        self.assertEqual(self.purchase.get_derived_state_from_commands(), Purchase.State.SUCCESSFUL)
        
        # With running commands, should remain SUCCESSFUL
        job1.state = CommandJob.State.RUNNING
        job1.save()
        self.assertEqual(self.purchase.get_derived_state_from_commands(), Purchase.State.SUCCESSFUL)
        
        # With failed commands, should be COMMAND_FAILED
        job1.state = CommandJob.State.FAILED
        job1.save()
        self.assertEqual(self.purchase.get_derived_state_from_commands(), Purchase.State.COMMAND_FAILED)
        
        # With all successful commands, should be FINISHED
        job1.state = CommandJob.State.SUCCESSFUL
        job1.save()
        self.assertEqual(self.purchase.get_derived_state_from_commands(), Purchase.State.FINISHED)

    @patch('shop.tasks.send_command_to_minecraft_server')
    def test_execute_single_command_success(self, mock_send_command):
        """Test successful execution of a single command"""
        mock_send_command.return_value = True
        
        # Create a command job
        job = CommandJob.objects.create(
            purchase_item=self.purchase_item,
            command_text="give testuser diamond 1",
            sequence_order=1,
            state=CommandJob.State.PENDING
        )
        
        # Execute the command
        result = execute_single_command(job.id)
        
        # Check result
        self.assertTrue(result)
        
        # Refresh job from database
        job.refresh_from_db()
        
        # Check job state
        self.assertEqual(job.state, CommandJob.State.SUCCESSFUL)
        self.assertIsNotNone(job.started_at)
        self.assertIsNotNone(job.completed_at)
        self.assertIsNone(job.error_message)
        
        # Check that the command was called correctly
        mock_send_command.assert_called_once_with(self.server, "give testuser diamond 1")

    @patch('shop.tasks.send_command_to_minecraft_server')
    def test_execute_single_command_failure(self, mock_send_command):
        """Test failed execution of a single command"""
        mock_send_command.return_value = False
        
        # Create a command job
        job = CommandJob.objects.create(
            purchase_item=self.purchase_item,
            command_text="give testuser diamond 1",
            sequence_order=1,
            state=CommandJob.State.PENDING
        )
        
        # Execute the command
        result = execute_single_command(job.id)
        
        # Check result
        self.assertFalse(result)
        
        # Refresh job from database
        job.refresh_from_db()
        
        # Check job state
        self.assertEqual(job.state, CommandJob.State.FAILED)
        self.assertIsNotNone(job.started_at)
        self.assertIsNotNone(job.completed_at)
        self.assertEqual(job.error_message, "Command execution failed")

    def test_update_state_from_commands(self):
        """Test that purchase state is updated correctly"""
        # Create command jobs
        job = CommandJob.objects.create(
            purchase_item=self.purchase_item,
            command_text="give testuser diamond 1",
            sequence_order=1,
            state=CommandJob.State.SUCCESSFUL
        )
        
        # Update state from commands
        self.purchase.update_state_from_commands()
        
        # Refresh from database
        self.purchase.refresh_from_db()
        
        # Should be FINISHED
        self.assertEqual(self.purchase.state, Purchase.State.FINISHED)

    def test_replace_command_placeholders_username_only(self):
        """Test placeholder replacement with username only"""
        command = "give {username} diamond 1"
        result = replace_command_placeholders(command, "testuser")
        self.assertEqual(result, "give testuser diamond 1")

    def test_replace_command_placeholders_uid_only(self):
        """Test placeholder replacement with uid only"""
        command = "give player diamond 1 {uid}"
        result = replace_command_placeholders(command, "testuser")

        # Should contain a 8-character unique ID where {uid} was
        self.assertNotIn("{uid}", result)
        self.assertTrue(result.startswith("give player diamond 1 "))

        # Extract the UID part and verify it's 8 characters of alphanumeric
        uid_part = result.split(" ")[-1]
        self.assertEqual(len(uid_part), 8)
        self.assertTrue(re.match(r'^[a-f0-9]{8}$', uid_part))

    def test_replace_command_placeholders_both(self):
        """Test placeholder replacement with both username and uid"""
        command = "give {username} diamond 1 {uid}"
        result = replace_command_placeholders(command, "testuser")

        # Should replace username
        self.assertIn("testuser", result)
        self.assertNotIn("{username}", result)

        # Should replace uid with 8-character unique ID
        self.assertNotIn("{uid}", result)
        parts = result.split(" ")
        self.assertEqual(parts[0], "give")
        self.assertEqual(parts[1], "testuser")
        self.assertEqual(parts[2], "diamond")
        self.assertEqual(parts[3], "1")

        # Last part should be the UID
        uid_part = parts[4]
        self.assertEqual(len(uid_part), 8)
        self.assertTrue(re.match(r'^[a-f0-9]{8}$', uid_part))

    def test_replace_command_placeholders_multiple_uids(self):
        """Test that multiple {uid} placeholders get different unique IDs"""
        command = "command {uid} and {uid}"
        result = replace_command_placeholders(command, "testuser")

        # Extract both UIDs
        parts = result.split(" ")
        uid1 = parts[1]
        uid2 = parts[3]

        # Both should be 8-character hex strings
        self.assertEqual(len(uid1), 8)
        self.assertEqual(len(uid2), 8)
        self.assertTrue(re.match(r'^[a-f0-9]{8}$', uid1))
        self.assertTrue(re.match(r'^[a-f0-9]{8}$', uid2))

        # They should be different
        self.assertNotEqual(uid1, uid2)

    def test_create_command_jobs_with_uid_placeholder(self):
        """Test that command jobs are created correctly with {uid} placeholder"""
        # Create item with commands containing {uid}
        item_with_uid = Item.objects.create(
            name="test_item_uid",
            display_name="Test Item with UID",
            description="A test item with UID placeholder",
            category=self.category,
            price=100,
            commands="give {username} diamond 1 {uid},setblock ~ ~ ~ chest{CustomName:'{\"text\":\"Chest_{uid}\"}'}",
            minecraft_server=self.server,
            enabled=True,
            published=True
        )

        # Create purchase item
        purchase_item_uid = PurchaseItem.objects.create(
            purchase=self.purchase,
            item=item_with_uid,
            quantity=1
        )

        # Create command jobs for the purchase
        create_command_jobs_for_purchase(self.purchase.id)

        # Get the command jobs for this specific item
        jobs = CommandJob.objects.filter(purchase_item=purchase_item_uid).order_by('sequence_order')
        self.assertEqual(jobs.count(), 2)

        # Check first command
        first_job = jobs[0]
        self.assertIn("testuser", first_job.command_text)
        self.assertNotIn("{username}", first_job.command_text)
        self.assertNotIn("{uid}", first_job.command_text)

        # Extract UID from first command
        first_parts = first_job.command_text.split(" ")
        first_uid = first_parts[-1]
        self.assertEqual(len(first_uid), 8)
        self.assertTrue(re.match(r'^[a-f0-9]{8}$', first_uid))

        # Check second command
        second_job = jobs[1]
        self.assertNotIn("{uid}", second_job.command_text)

        # Extract UID from second command (it's in the JSON part)
        self.assertTrue("Chest_" in second_job.command_text)
        # Find the UID in the chest name
        uid_match = re.search(r'Chest_([a-f0-9]{8})', second_job.command_text)
        self.assertIsNotNone(uid_match)
        second_uid = uid_match.group(1)
        self.assertEqual(len(second_uid), 8)

        # UIDs should be different between commands
        self.assertNotEqual(first_uid, second_uid)

    @patch('shop.tasks.send_command_to_minecraft_server')
    def test_revoke_commands_with_uid_placeholder(self, mock_send_command):
        """Test that revoke commands work correctly with {uid} placeholder"""
        from shop.tasks import revoke_purchase_item

        mock_send_command.return_value = True

        # Create item with revoke commands containing {uid}
        item_with_revoke_uid = Item.objects.create(
            name="test_item_revoke_uid",
            display_name="Test Item with Revoke UID",
            description="A test item with UID placeholder in revoke commands",
            category=self.category,
            price=100,
            commands="give {username} diamond 1",
            revoke_commands="clear {username},say Revoked item {uid}",
            minecraft_server=self.server,
            expiration_days=1,  # Make it expirable
            enabled=True,
            published=True
        )

        # Create purchase item that's expired
        purchase_item_revoke = PurchaseItem.objects.create(
            purchase=self.purchase,
            item=item_with_revoke_uid,
            quantity=1,
            expires_at=timezone.now() - timezone.timedelta(days=1),  # Already expired
            subscription_status=Purchase.SubscriptionStatus.SUBSCRIBED
        )

        # Run revoke task for this specific purchase item
        revoke_purchase_item(purchase_item_revoke.id)

        # Check that send_command_to_minecraft_server was called
        self.assertTrue(mock_send_command.called)

        # Get the calls made to send_command_to_minecraft_server
        calls = mock_send_command.call_args_list

        # Should have 2 calls (2 revoke commands)
        self.assertEqual(len(calls), 2)

        # Check first call (clear command)
        first_call_args = calls[0][0]
        first_command = first_call_args[1]
        self.assertEqual(first_command, "clear testuser")

        # Check second call (say command with UID)
        second_call_args = calls[1][0]
        second_command = second_call_args[1]
        self.assertTrue(second_command.startswith("say Revoked item "))
        self.assertNotIn("{uid}", second_command)
        self.assertNotIn("{username}", second_command)

        # Extract and verify UID
        uid_part = second_command.split(" ")[-1]
        self.assertEqual(len(uid_part), 8)
        self.assertTrue(re.match(r'^[a-f0-9]{8}$', uid_part))
