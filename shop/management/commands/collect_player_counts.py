from django.core.management.base import BaseCommand
from shop.tasks import collect_player_counts


class Command(BaseCommand):
    help = 'Manually run the player count collection task'

    def handle(self, *args, **options):
        self.stdout.write('Running player count collection task...')
        
        successful_queries = collect_player_counts()
        
        if successful_queries > 0:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully collected player counts from {successful_queries} server(s).'
                )
            )
        else:
            self.stdout.write(
                self.style.WARNING(
                    'No servers were successfully queried. Check server configurations and connectivity.'
                )
            )
