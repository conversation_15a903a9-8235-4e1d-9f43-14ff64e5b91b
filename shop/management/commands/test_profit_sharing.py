from decimal import Decimal
from django.core.management.base import BaseCommand
from django.utils import timezone
from shop.models import Purchase, PurchaseItem, Item, Category, ContentCreator, MinecraftServer


class Command(BaseCommand):
    help = 'Test the profit-sharing system with sample data'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Creating test data for profit-sharing system...'))
        
        # Create or get test content creator
        creator, created = ContentCreator.objects.get_or_create(
            name="TestCreator",
            defaults={
                'display_name': "Test Creator",
                'commission_rate': Decimal('25.00'),
                'admin_notes': "Test creator for profit-sharing system",
                'enabled': True,
                'published': True
            }
        )
        if created:
            self.stdout.write(f'Created ContentCreator: {creator.name}')
        else:
            self.stdout.write(f'Using existing ContentCreator: {creator.name}')
        
        # Create or get test category
        category, created = Category.objects.get_or_create(
            name="TestCategory",
            defaults={
                'display_name': "Test Category",
                'enabled': True,
                'published': True
            }
        )
        if created:
            self.stdout.write(f'Created Category: {category.name}')
        
        # Create or get test server
        server, created = MinecraftServer.objects.get_or_create(
            name="TestServer",
            defaults={
                'display_name': "Test Server",
                'ip': "127.0.0.1",
                'port': 25565,
                'enabled': True,
                'published': True
            }
        )
        if created:
            self.stdout.write(f'Created MinecraftServer: {server.name}')
        
        # Create test items
        item1, created = Item.objects.get_or_create(
            name="TestItem1",
            defaults={
                'display_name': "Test Item 1",
                'category': category,
                'price': 1000,  # 10.00 in currency
                'minecraft_server': server,
                'enabled': True,
                'published': True
            }
        )
        if created:
            self.stdout.write(f'Created Item: {item1.name}')
        
        item2, created = Item.objects.get_or_create(
            name="TestItem2",
            defaults={
                'display_name': "Test Item 2",
                'category': category,
                'price': 2000,  # 20.00 in currency
                'minecraft_server': server,
                'enabled': True,
                'published': True
            }
        )
        if created:
            self.stdout.write(f'Created Item: {item2.name}')
        
        # Create test purchases with different scenarios
        
        # Scenario 1: Completed purchase with referrer (should calculate commission)
        purchase1 = Purchase.objects.create(
            minecraft_username="testuser1",
            referrer=creator,
            state=Purchase.State.SUCCESSFUL
        )
        PurchaseItem.objects.create(
            purchase=purchase1,
            item=item1,
            quantity=2
        )
        # Set payment success after items are created
        purchase1.payment_succeeded_at = timezone.now()
        purchase1.save()
        self.stdout.write(f'Created Purchase 1: {purchase1.ref_id} (with referrer, completed)')
        
        # Scenario 2: Completed purchase without referrer (no commission)
        purchase2 = Purchase.objects.create(
            minecraft_username="testuser2",
            state=Purchase.State.SUCCESSFUL
        )
        PurchaseItem.objects.create(
            purchase=purchase2,
            item=item2,
            quantity=1
        )
        # Set payment success after items are created
        purchase2.payment_succeeded_at = timezone.now()
        purchase2.save()
        self.stdout.write(f'Created Purchase 2: {purchase2.ref_id} (no referrer, completed)')
        
        # Scenario 3: Pending purchase with referrer (no commission yet)
        purchase3 = Purchase.objects.create(
            minecraft_username="testuser3",
            referrer=creator,
            state=Purchase.State.CREATED
        )
        PurchaseItem.objects.create(
            purchase=purchase3,
            item=item1,
            quantity=1
        )
        self.stdout.write(f'Created Purchase 3: {purchase3.ref_id} (with referrer, pending)')
        
        # Scenario 4: Multiple items purchase with referrer
        purchase4 = Purchase.objects.create(
            minecraft_username="testuser4",
            referrer=creator,
            state=Purchase.State.SUCCESSFUL
        )
        PurchaseItem.objects.create(
            purchase=purchase4,
            item=item1,
            quantity=1
        )
        PurchaseItem.objects.create(
            purchase=purchase4,
            item=item2,
            quantity=2
        )
        # Set payment success after items are created
        purchase4.payment_succeeded_at = timezone.now()
        purchase4.save()
        self.stdout.write(f'Created Purchase 4: {purchase4.ref_id} (multiple items, with referrer)')
        
        # Display results
        self.stdout.write(self.style.SUCCESS('\n--- Profit Sharing Results ---'))
        
        # Refresh creator from database to get updated calculations
        creator.refresh_from_db()
        
        self.stdout.write(f'ContentCreator: {creator.name}')
        self.stdout.write(f'  Commission Rate: {creator.commission_rate}%')
        self.stdout.write(f'  Total Earned: {creator.total_earned}')
        self.stdout.write(f'  Total Settled: {creator.total_settled}')
        self.stdout.write(f'  Total Unsettled: {creator.total_unsettled}')
        
        self.stdout.write('\nPurchases with commissions:')
        for purchase in Purchase.objects.filter(referral_commission__isnull=False):
            self.stdout.write(f'  {purchase.ref_id}: {purchase.referral_commission} ({purchase.referral_settlement_status})')
        
        self.stdout.write(self.style.SUCCESS('\nTest data created successfully!'))
        self.stdout.write('You can now:')
        self.stdout.write('1. Check the admin panel at /admin/')
        self.stdout.write('2. View ContentCreator earnings in the admin')
        self.stdout.write('3. Test the settlement action on purchases')
