from django.core.management.base import BaseCommand
from django_q.tasks import schedule
from django_q.models import Schedule


class Command(BaseCommand):
    help = 'Set up recurring task to collect player count snapshots'

    def add_arguments(self, parser):
        parser.add_argument(
            '--minutes',
            type=int,
            default=1,
            help='Interval in minutes between player count collections (default: 1)'
        )

    def handle(self, *args, **options):
        minutes = options['minutes']
        
        # Check if the task is already scheduled
        existing_task = Schedule.objects.filter(
            func='shop.tasks.collect_player_counts',
            schedule_type=Schedule.MINUTES
        ).first()

        if existing_task:
            self.stdout.write(
                self.style.WARNING(
                    f'Player count collection task already exists with ID {existing_task.id}. '
                    f'It runs every {existing_task.minutes} minutes.'
                )
            )
            
            # Ask if user wants to update the interval
            if existing_task.minutes != minutes:
                self.stdout.write(
                    self.style.WARNING(
                        f'Current interval is {existing_task.minutes} minutes, '
                        f'but you specified {minutes} minutes.'
                    )
                )
                
                # Update the existing task
                existing_task.minutes = minutes
                existing_task.save()
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Updated player count collection task interval to {minutes} minutes.'
                    )
                )
            return

        # Schedule the task to run every specified minutes
        task = schedule(
            'shop.tasks.collect_player_counts',
            schedule_type=Schedule.MINUTES,
            minutes=minutes,
            repeats=-1  # Run indefinitely
        )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully scheduled player count collection task with ID {task.id}. '
                f'It will run every {minutes} minutes to collect server player counts.'
            )
        )
