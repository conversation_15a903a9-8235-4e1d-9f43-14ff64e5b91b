from django.core.management.base import BaseCommand
from shop.models import CommandJob, Purchase
from django_q.tasks import async_task


class Command(BaseCommand):
    help = 'Retry failed command jobs'

    def add_arguments(self, parser):
        parser.add_argument(
            '--purchase-id',
            type=int,
            help='Retry failed jobs for a specific purchase ID',
        )
        parser.add_argument(
            '--job-id',
            type=int,
            help='Retry a specific command job ID',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Retry all failed command jobs',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be retried without actually doing it',
        )

    def handle(self, *args, **options):
        purchase_id = options.get('purchase_id')
        job_id = options.get('job_id')
        retry_all = options.get('all')
        dry_run = options.get('dry_run')

        if job_id:
            self.retry_single_job(job_id, dry_run)
        elif purchase_id:
            self.retry_purchase_jobs(purchase_id, dry_run)
        elif retry_all:
            self.retry_all_failed_jobs(dry_run)
        else:
            self.stdout.write(
                self.style.ERROR(
                    'Please specify --job-id, --purchase-id, or --all'
                )
            )

    def retry_single_job(self, job_id, dry_run):
        """Retry a specific command job"""
        try:
            job = CommandJob.objects.get(id=job_id)

            if job.state != CommandJob.State.FAILED:
                self.stdout.write(
                    self.style.WARNING(
                        f'Job {job_id} is not in failed state (current: {job.state})'
                    )
                )
                return

            if dry_run:
                self.stdout.write(
                    f'Would retry job {job_id}: {job.command_text[:50]}...'
                )
            else:
                # Reset job state to pending
                job.state = CommandJob.State.PENDING
                job.error_message = None
                job.started_at = None
                job.completed_at = None
                job.save()

                # Queue the job for execution
                async_task('shop.tasks.execute_single_command', job_id)

                self.stdout.write(
                    self.style.SUCCESS(f'Retrying job {job_id}')
                )

        except CommandJob.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Command job {job_id} not found')
            )

    def retry_purchase_jobs(self, purchase_id, dry_run):
        """Retry all failed jobs for a specific purchase"""
        try:
            purchase = Purchase.objects.get(id=purchase_id)
            failed_jobs = CommandJob.objects.filter(
                purchase_item__purchase=purchase,
                state=CommandJob.State.FAILED
            )

            if not failed_jobs.exists():
                self.stdout.write(
                    self.style.WARNING(
                        f'No failed jobs found for purchase {purchase_id}'
                    )
                )
                return

            failed_jobs_count = failed_jobs.count()
            self.stdout.write(
                f'Found {failed_jobs_count} failed jobs for purchase {purchase_id}'
            )
            
            if dry_run:
                for job in failed_jobs:
                    self.stdout.write(
                        f'Would retry job {job.id}: {job.command_text[:50]}...'
                    )
            else:
                # Reset all failed jobs to pending
                failed_jobs.update(
                    state=CommandJob.State.PENDING,
                    error_message=None,
                    started_at=None,
                    completed_at=None
                )
                
                # Queue purchase for re-execution
                async_task('shop.tasks.execute_purchase_commands', purchase_id)
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Retrying {failed_jobs_count} failed jobs for purchase {purchase_id}'
                    )
                )

        except Purchase.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Purchase {purchase_id} not found')
            )

    def retry_all_failed_jobs(self, dry_run):
        """Retry all failed command jobs"""
        failed_jobs = CommandJob.objects.filter(state=CommandJob.State.FAILED)

        if not failed_jobs.exists():
            self.stdout.write(
                self.style.WARNING('No failed jobs found')
            )
            return

        # Group by purchase for better organization
        purchases_with_failed_jobs = Purchase.objects.filter(
            purchase_items__command_jobs__state=CommandJob.State.FAILED
        ).distinct()

        failed_jobs_count = failed_jobs.count()
        purchases_count = purchases_with_failed_jobs.count()

        self.stdout.write(
            f'Found {failed_jobs_count} failed jobs across '
            f'{purchases_count} purchases'
        )

        if dry_run:
            for purchase in purchases_with_failed_jobs:
                purchase_failed_jobs = failed_jobs.filter(
                    purchase_item__purchase=purchase
                )
                self.stdout.write(
                    f'Purchase {purchase.id}: {purchase_failed_jobs.count()} failed jobs'
                )
                for job in purchase_failed_jobs:
                    self.stdout.write(
                        f'  Would retry job {job.id}: {job.command_text[:50]}...'
                    )
        else:
            # Reset all failed jobs to pending
            failed_jobs.update(
                state=CommandJob.State.PENDING,
                error_message=None,
                started_at=None,
                completed_at=None
            )

            # Queue each purchase for re-execution
            for purchase in purchases_with_failed_jobs:
                async_task('shop.tasks.execute_purchase_commands', purchase.id)

            self.stdout.write(
                self.style.SUCCESS(
                    f'Retrying {failed_jobs_count} failed jobs across '
                    f'{purchases_count} purchases'
                )
            )
