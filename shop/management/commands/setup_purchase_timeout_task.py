from django.core.management.base import BaseCommand
from django_q.tasks import schedule
from django_q.models import Schedule


class Command(BaseCommand):
    help = 'Set up recurring task to timeout unpaid purchases'

    def handle(self, *args, **options):
        # Check if the task is already scheduled
        existing_task = Schedule.objects.filter(
            func='shop.tasks.timeout_unpaid_purchases',
            schedule_type=Schedule.MINUTES
        ).first()

        if existing_task:
            self.stdout.write(
                self.style.WARNING(
                    f'Purchase timeout task already exists with ID {existing_task.id}. '
                    f'It runs every {existing_task.minutes} minutes.'
                )
            )
            return

        # Schedule the task to run every 5 minutes
        task = schedule(
            'shop.tasks.timeout_unpaid_purchases',
            schedule_type=Schedule.MINUTES,
            minutes=5,
            repeats=-1  # Run indefinitely
        )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully scheduled purchase timeout task with ID {task.id}. '
                f'It will run every 5 minutes to check for purchases older than 10 minutes.'
            )
        )
