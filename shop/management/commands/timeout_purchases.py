from django.core.management.base import BaseCommand
from shop.tasks import timeout_unpaid_purchases


class Command(BaseCommand):
    help = 'Manually run the purchase timeout task'

    def handle(self, *args, **options):
        self.stdout.write('Running purchase timeout task...')
        
        timeout_count = timeout_unpaid_purchases()
        
        if timeout_count > 0:
            self.stdout.write(
                self.style.SUCCESS(f'Successfully marked {timeout_count} purchases as timeout')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('No purchases needed to be timed out')
            )
