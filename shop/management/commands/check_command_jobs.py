from django.core.management.base import BaseCommand
from shop.models import Purchase, PurchaseItem, CommandJob
from django.db.models import Count


class Command(BaseCommand):
    help = 'Check the status of command jobs for purchases'

    def add_arguments(self, parser):
        parser.add_argument(
            '--purchase-id',
            type=int,
            help='Check jobs for a specific purchase ID',
        )
        parser.add_argument(
            '--failed-only',
            action='store_true',
            help='Show only failed command jobs',
        )
        parser.add_argument(
            '--pending-only',
            action='store_true',
            help='Show only pending command jobs',
        )

    def handle(self, *args, **options):
        purchase_id = options.get('purchase_id')
        failed_only = options.get('failed_only')
        pending_only = options.get('pending_only')

        if purchase_id:
            try:
                purchase = Purchase.objects.get(id=purchase_id)
                self.show_purchase_status(purchase)
            except Purchase.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Purchase with ID {purchase_id} not found')
                )
                return
        else:
            self.show_overall_status(failed_only, pending_only)

    def show_purchase_status(self, purchase):
        """Show detailed status for a specific purchase"""
        self.stdout.write(
            self.style.SUCCESS(f'\n=== Purchase {purchase.id} Status ===')
        )
        self.stdout.write(f'Username: {purchase.minecraft_username}')
        self.stdout.write(f'State: {purchase.state}')
        self.stdout.write(f'Derived State: {purchase.get_derived_state_from_commands()}')
        
        for purchase_item in purchase.purchase_items.all():
            self.stdout.write(f'\n--- Item: {purchase_item.item.name} (x{purchase_item.quantity}) ---')
            self.stdout.write(f'Command Execution State: {purchase_item.get_command_execution_state()}')
            
            command_jobs = purchase_item.command_jobs.all().order_by('sequence_order')
            if command_jobs.exists():
                for job in command_jobs:
                    status_color = self.get_status_color(job.state)
                    self.stdout.write(
                        f'  {job.sequence_order}. {job.command_text[:50]}... - '
                        f'{status_color(job.state.upper())}'
                    )
                    if job.error_message:
                        self.stdout.write(f'     Error: {job.error_message}')
            else:
                self.stdout.write('  No command jobs found')

    def show_overall_status(self, failed_only, pending_only):
        """Show overall status of all command jobs"""
        self.stdout.write(self.style.SUCCESS('\n=== Command Jobs Overview ==='))
        
        # Get job counts by state
        job_counts = CommandJob.objects.values('state').annotate(count=Count('id'))
        
        self.stdout.write('\nJob Counts by State:')
        for item in job_counts:
            status_color = self.get_status_color(item['state'])
            self.stdout.write(f'  {status_color(item["state"].upper())}: {item["count"]}')
        
        # Show recent purchases with their command states
        recent_purchases = Purchase.objects.filter(
            state__in=[Purchase.State.SUCCESSFUL, Purchase.State.COMMAND_FAILED, Purchase.State.FINISHED]
        ).order_by('-created_at')[:10]
        
        self.stdout.write('\nRecent Purchases:')
        for purchase in recent_purchases:
            derived_state = purchase.get_derived_state_from_commands()
            state_color = self.get_purchase_state_color(derived_state)
            self.stdout.write(
                f'  Purchase {purchase.id} ({purchase.minecraft_username}) - '
                f'{state_color(derived_state.upper())}'
            )
        
        # Show failed jobs if requested
        if failed_only:
            self.show_failed_jobs()
        
        # Show pending jobs if requested
        if pending_only:
            self.show_pending_jobs()

    def show_failed_jobs(self):
        """Show all failed command jobs"""
        failed_jobs = CommandJob.objects.filter(state=CommandJob.State.FAILED).order_by('-created_at')
        
        self.stdout.write(self.style.ERROR('\n=== Failed Command Jobs ==='))
        if failed_jobs.exists():
            for job in failed_jobs:
                self.stdout.write(
                    f'Job {job.id}: {job.command_text[:50]}... '
                    f'(Purchase {job.purchase_item.purchase.id})'
                )
                if job.error_message:
                    self.stdout.write(f'  Error: {job.error_message}')
        else:
            self.stdout.write('No failed jobs found')

    def show_pending_jobs(self):
        """Show all pending command jobs"""
        pending_jobs = CommandJob.objects.filter(state=CommandJob.State.PENDING).order_by('created_at')
        
        self.stdout.write(self.style.WARNING('\n=== Pending Command Jobs ==='))
        if pending_jobs.exists():
            for job in pending_jobs:
                self.stdout.write(
                    f'Job {job.id}: {job.command_text[:50]}... '
                    f'(Purchase {job.purchase_item.purchase.id})'
                )
        else:
            self.stdout.write('No pending jobs found')

    def get_status_color(self, state):
        """Get appropriate color styling for job state"""
        if state == CommandJob.State.SUCCESSFUL:
            return self.style.SUCCESS
        elif state == CommandJob.State.FAILED:
            return self.style.ERROR
        elif state == CommandJob.State.RUNNING:
            return self.style.WARNING
        elif state == CommandJob.State.EMPTY:
            return self.style.MIGRATE_LABEL  # Gray color for empty
        else:  # PENDING
            return self.style.NOTICE

    def get_purchase_state_color(self, state):
        """Get appropriate color styling for purchase state"""
        if state == Purchase.State.FINISHED:
            return self.style.SUCCESS
        elif state == Purchase.State.COMMAND_FAILED:
            return self.style.ERROR
        else:
            return self.style.WARNING
