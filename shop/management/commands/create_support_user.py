from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from shop.models import UserProfile


class Command(BaseCommand):
    help = 'Create a support user for testing'

    def add_arguments(self, parser):
        parser.add_argument('username', type=str, help='Username for the support user')
        parser.add_argument('--email', type=str, help='Email for the support user')
        parser.add_argument('--password', type=str, default='support123', help='Password for the support user')

    def handle(self, *args, **options):
        username = options['username']
        email = options.get('email', f'{username}@example.com')
        password = options['password']

        # Check if user already exists
        if User.objects.filter(username=username).exists():
            self.stdout.write(
                self.style.ERROR(f'User "{username}" already exists')
            )
            return

        # Create user
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password,
            is_staff=True  # Required to access admin
        )

        # Set support flag
        user.profile.is_support = True
        user.profile.save()

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created support user "{username}" with password "{password}"'
            )
        )
        self.stdout.write(
            f'The user can now log into the admin panel and search purchases by reference ID.'
        )
