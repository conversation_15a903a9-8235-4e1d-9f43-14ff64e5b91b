import json
import logging
import requests
from django.conf import settings
from typing import Dict, Optional, Tuple

logger = logging.getLogger(__name__)


class ZarinpalError(Exception):
    """Custom exception for Zarinpal API errors"""
    pass


class ZarinpalService:
    """Service class for Zarinpal payment gateway integration"""

    def __init__(self):
        self.merchant_id = settings.ZARINPAL_MERCHANT_ID
        self.sandbox = settings.ZARINPAL_SANDBOX

        if self.sandbox:
            self.base_url = "https://sandbox.zarinpal.com/pg/v4/payment/"
            self.payment_url = "https://sandbox.zarinpal.com/pg/StartPay/"
        else:
            self.base_url = "https://payment.zarinpal.com/pg/v4/payment/"
            self.payment_url = "https://payment.zarinpal.com/pg/StartPay/"

    def request_payment(self, amount: int, description: str, callback_url: str,
                       mobile: Optional[str] = None, email: Optional[str] = None,
                       order_id: Optional[str] = None) -> Tuple[bool, Dict]:
        """
        Request payment from Zarinpal

        Args:
            amount: Payment amount in Rials
            description: Payment description
            callback_url: Callback URL for payment result
            mobile: Optional mobile number
            email: Optional email
            order_id: Optional order ID

        Returns:
            Tuple of (success: bool, response_data: dict)
        """
        url = f"{self.base_url}request.json"

        payload = {
            "merchant_id": self.merchant_id,
            "amount": amount,
            "description": description,
            "callback_url": callback_url,
        }

        # Add metadata if provided
        metadata = {}
        if mobile:
            metadata["mobile"] = mobile
        if email:
            metadata["email"] = email
        if order_id:
            metadata["order_id"] = order_id

        if metadata:
            payload["metadata"] = metadata

        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }

        try:
            logger.info(f"Requesting payment from Zarinpal for order_id: {order_id} for amount: {amount}")
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            response.raise_for_status()

            data = response.json()

            if 'data' in data and data['data'].get('code') == 100:
                logger.info(f"Payment request successful. Authority: {data['data']['authority']}")
                return True, data['data']
            else:
                error_msg = f"Zarinpal request failed: {data}"
                logger.error(error_msg)
                return False, data

        except requests.exceptions.RequestException as e:
            error_msg = f"Network error during Zarinpal request: {str(e)}"
            logger.error(error_msg)
            return False, {"error": error_msg}
        except json.JSONDecodeError as e:
            error_msg = f"Invalid JSON response from Zarinpal: {str(e)}"
            logger.error(error_msg)
            return False, {"error": error_msg}
        except Exception as e:
            error_msg = f"Unexpected error during Zarinpal request: {str(e)}"
            logger.error(error_msg)
            return False, {"error": error_msg}

    def verify_payment(self, authority: str, amount: int) -> Tuple[bool, Dict]:
        """
        Verify payment with Zarinpal

        Args:
            authority: Authority code from payment request
            amount: Payment amount in Rials

        Returns:
            Tuple of (success: bool, response_data: dict)
        """
        url = f"{self.base_url}verify.json"

        payload = {
            "merchant_id": self.merchant_id,
            "amount": amount,
            "authority": authority
        }

        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }

        try:
            logger.info(f"Verifying payment with Zarinpal. Authority: {authority}")
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            response.raise_for_status()

            data = response.json()

            # Code 100 = successful payment (first time), Code 101 = already verified before
            if 'data' in data and data['data'].get('code') in [100, 101]:
                code = data['data'].get('code')
                ref_id = data['data'].get('ref_id')
                if code == 100:
                    logger.info(f"Payment verification successful (first time). Ref ID: {ref_id}")
                elif code == 101:
                    logger.info(f"Payment already verified before. Ref ID: {ref_id}")
                return True, data['data']
            else:
                error_msg = f"Zarinpal verification failed: {data}"
                logger.error(error_msg)
                return False, data

        except requests.exceptions.RequestException as e:
            error_msg = f"Network error during Zarinpal verification: {str(e)}"
            logger.error(error_msg)
            return False, {"error": error_msg}
        except json.JSONDecodeError as e:
            error_msg = f"Invalid JSON response from Zarinpal: {str(e)}"
            logger.error(error_msg)
            return False, {"error": error_msg}
        except Exception as e:
            error_msg = f"Unexpected error during Zarinpal verification: {str(e)}"
            logger.error(error_msg)
            return False, {"error": error_msg}

    def get_payment_url(self, authority: str) -> str:
        """
        Generate payment URL for redirecting user to Zarinpal

        Args:
            authority: Authority code from payment request

        Returns:
            Payment URL
        """
        return f"{self.payment_url}{authority}"


# Convenience functions for easy usage
def request_payment(amount: int, description: str, callback_url: str,
                   mobile: Optional[str] = None, email: Optional[str] = None,
                   order_id: Optional[str] = None) -> Tuple[bool, Dict]:
    """Convenience function to request payment"""
    service = ZarinpalService()
    return service.request_payment(amount, description, callback_url, mobile, email, order_id)


def verify_payment(authority: str, amount: int) -> Tuple[bool, Dict]:
    """Convenience function to verify payment"""
    service = ZarinpalService()
    return service.verify_payment(authority, amount)


def get_payment_url(authority: str) -> str:
    """Convenience function to get payment URL"""
    service = ZarinpalService()
    return service.get_payment_url(authority)
