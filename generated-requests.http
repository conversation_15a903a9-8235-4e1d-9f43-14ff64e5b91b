@host=localhost:8000
#@host=api.ucraft.ir

### create purchase with multiple items (with subscription support)
POST http://{{host}}/api/purchase/
content-type: application/json

{
  "minecraft_username": "something",
  "mobile_number": "1234567890",
  "items": [
    {"item": 4, "quantity": 1},
    {"item": 5, "quantity": 1}
  ],
  "referrer": null
}

### create purchase with single item
POST http://{{host}}/api/purchase/
content-type: application/json

{
  "minecraft_username": "raiden",
  "mobile_number": "1234567890",
  "items": [
    {"item": 4, "quantity": 1}
  ],
  "referrer": null
}

<> 2025-05-27T232454.200.json
<> 2025-05-27T232444.400.json
<> 2025-05-27T232325.500.html


### mock IPG callback
POST http://{{host}}/api/ipg/callback/
content-type: application/json

{
  "purchase_id": 1,
  "status": "success"
}

### shop
GET https://{{host}}/api/shop/

<> 2025-05-29T142146.200.json
<> 2025-05-27T232042.200.json
<> 2025-05-27T232030.200.json
<> 2025-05-27T231943.200.json
<> 2025-05-27T231301.200.json
<> 2025-05-22T215029.200.json

### home
GET http://{{host}}/api/home/

<> 2025-05-29T164126.200.json
<> 2025-05-29T164119.200.json
<> 2025-05-27T231249.200.json
<> 2025-05-23T044021.200.json
<> 2025-05-23T044002.200.json
<> 2025-05-23T043856.200.json
<> 2025-05-23T043838.200.json
<> 2025-05-23T043831.200.json
<> 2025-05-23T043631.200.json
<> 2025-05-23T043624.200.json
<> 2025-05-23T043605.200.json
<> 2025-05-23T043517.200.json
<> 2025-05-23T043459.200.json
<> 2025-05-23T014929.200.json

### check username
@username=raiden
GET http://{{host}}/api/check-username/?username={{username}}

<> 2025-05-27T232245.200.json
<> 2025-05-27T232239.200.json
<> 2025-05-27T232224.200.json
<> 2025-05-23T144903.200.json
<> 2025-05-23T144855.200.json
<> 2025-05-23T144847.200.json
<> 2025-05-23T144839.200.json
<> 2025-05-23T143537.200.json
<> 2025-05-23T142755.200.json
<> 2025-05-23T142735.200.json
<> 2025-05-23T142708.400.json
<> 2025-05-23T142621.200.json
<> 2025-05-23T142414.200.json

### Online players count
GET http://{{host}}/api/online-players/

### Purchase history of a username
GET http://{{host}}/api/purchase-history/?username={{username}}