### Purchase History API Tests
### New API endpoint to retrieve purchases for a Minecraft username with pagination

### Test 1: Get purchase history for existing user
GET http://localhost:8000/api/purchase-history/?username=testuser

### Test 2: Get purchase history with pagination (page 2)
GET http://localhost:8000/api/purchase-history/?username=testuser&page=2

### Test 3: Get purchase history for non-existent user
GET http://localhost:8000/api/purchase-history/?username=nonexistentuser

### Test 4: Test error case - missing username parameter
GET http://localhost:8000/api/purchase-history/

### Test 5: Test with custom page size (max 100)
GET http://localhost:8000/api/purchase-history/?username=testuser&page_size=10

### Expected Response Format:
### {
###   "count": 1,
###   "next": null,
###   "previous": null,
###   "results": [
###     {
###       "ref_id": "67126620",
###       "created_at": "2025-06-10T20:02:26.938430+03:30",
###       "payment_succeeded_at": null,
###       "command_run_status": "successful",
###       "state": "timeout",
###       "payment_url": null,
###       "items": [
###         {
###           "item_display_name": "رنک آیرون - یک ماهه",
###           "quantity": 2,
###           "subscription_status": "onetime",
###           "expires_at": null
###         }
###       ]
###     }
###   ]
### }
