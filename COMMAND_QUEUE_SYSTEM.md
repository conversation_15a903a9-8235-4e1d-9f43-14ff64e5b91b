# Command Queue System Documentation

## Overview

The MCShop application now uses a Django-Q based queue system for executing Minecraft commands instead of running them directly during the payment callback. This provides better reliability, monitoring, and error handling.

## How It Works

### 1. Payment Flow Changes

**Before**: Commands were executed directly in `IPGCallbackView` during payment verification.

**Now**: After successful payment verification, command jobs are created and queued for execution.

### 2. New Models

#### CommandJob
- Tracks individual command execution
- States: `pending`, `running`, `successful`, `failed`
- Stores command text, execution times, and error messages
- Links to PurchaseItem with sequence order

### 3. State Management

#### Purchase States
- **SUCCESSFUL**: Payment verified, commands are being executed
- **FINISHED**: All commands executed successfully
- **COMMAND_FAILED**: Some commands failed during execution
- **FAILED**: Payment failed
- **CREATED**: Payment not yet completed
- **TIMEOUT**: Payment timed out

#### Command Execution States
- **pending**: Command waiting to be executed
- **running**: Command currently being executed
- **successful**: Command executed successfully
- **failed**: Command execution failed

### 4. Sequential Execution

Commands are executed sequentially:
1. All commands for a purchase are created as CommandJob instances
2. Commands are executed in sequence order
3. If a command fails, remaining commands for that item are skipped
4. Purchase state is updated based on overall command execution status

## Key Components

### Tasks (`shop/tasks.py`)

- `create_command_jobs_for_purchase(purchase_id)`: Creates CommandJob instances
- `execute_purchase_commands(purchase_id)`: Orchestrates command execution
- `execute_purchase_item_commands(purchase_item_id)`: Executes commands for one item
- `execute_single_command(command_job_id)`: Executes a single command

### Models (`shop/models.py`)

- `CommandJob`: Tracks individual command execution
- `PurchaseItem.get_command_execution_state()`: Returns overall command state
- `Purchase.get_derived_state_from_commands()`: Derives purchase state from commands
- `Purchase.update_state_from_commands()`: Updates purchase state

### Admin Interface

- CommandJob admin for monitoring individual commands
- Enhanced PurchaseItem admin showing command states
- Enhanced Purchase admin showing derived states

## Management Commands

### Check Command Jobs Status
```bash
python manage.py check_command_jobs
python manage.py check_command_jobs --purchase-id 123
python manage.py check_command_jobs --failed-only
python manage.py check_command_jobs --pending-only
```

### Retry Failed Commands
```bash
python manage.py retry_failed_commands --all
python manage.py retry_failed_commands --purchase-id 123
python manage.py retry_failed_commands --job-id 456
python manage.py retry_failed_commands --dry-run --all
```

## Monitoring

### Django Admin
- View command execution status in Purchase and PurchaseItem admin
- Monitor individual CommandJob instances
- See error messages and execution times

### Command Line
- Use management commands to check status and retry failed jobs
- Monitor Django-Q cluster logs for task execution

### Database Queries
```python
# Get failed commands
failed_jobs = CommandJob.objects.filter(state=CommandJob.State.FAILED)

# Get purchases with command issues
problematic_purchases = Purchase.objects.filter(
    purchase_items__command_jobs__state=CommandJob.State.FAILED
).distinct()

# Check command execution progress
purchase = Purchase.objects.get(id=123)
print(f"Current state: {purchase.state}")
print(f"Derived state: {purchase.get_derived_state_from_commands()}")
```

## Error Handling

### Automatic Retry
- Failed commands can be retried using management commands
- Commands are reset to pending state and re-queued

### Error Logging
- All command execution is logged
- Error messages are stored in CommandJob instances
- Django-Q provides additional task monitoring

## Benefits

1. **Reliability**: Commands are not lost if there are temporary issues
2. **Monitoring**: Full visibility into command execution status
3. **Retry Capability**: Failed commands can be easily retried
4. **Sequential Execution**: Commands run in proper order
5. **Error Isolation**: Failed commands don't affect successful ones
6. **Scalability**: Queue system can handle high load

## Migration Notes

- Existing purchases are not affected
- New purchases automatically use the queue system
- No changes needed to item configuration
- Commands are still configured the same way in Item model

## Troubleshooting

### Commands Not Executing
1. Check Django-Q cluster is running: `python manage.py qcluster`
2. Check for failed jobs: `python manage.py check_command_jobs --failed-only`
3. Check server connectivity and RCON configuration

### Stuck Commands
1. Check for pending jobs: `python manage.py check_command_jobs --pending-only`
2. Restart Django-Q cluster if needed
3. Retry failed commands: `python manage.py retry_failed_commands --all`

### Performance Issues
1. Monitor Django-Q task queue length
2. Adjust worker count in settings if needed
3. Check database performance for CommandJob queries
