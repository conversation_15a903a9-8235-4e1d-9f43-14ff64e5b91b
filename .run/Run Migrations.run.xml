<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Run Migrations" type="PythonConfigurationType" factoryName="Python">
    <module name="mcshop" />
    <option name="ENV_FILES" value="" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="PARENT_ENVS" value="true" />
    <envs>
      <env name="PYTHONUNBUFFERED" value="1" />
      <env name="DJANGO_SETTINGS_MODULE" value="mcshop.settings" />
    </envs>
    <option name="SDK_HOME" value="" />
    <option name="SDK_NAME" value="Python 3.8 (mcshop)" />
    <option name="WORKING_DIRECTORY" value="C:\Users\<USER>\Development\mcshop" />
    <option name="IS_MODULE_SDK" value="false" />
    <option name="ADD_CONTENT_ROOTS" value="true" />
    <option name="ADD_SOURCE_ROOTS" value="true" />
    <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
    <option name="SCRIPT_NAME" value="$PROJECT_DIR$/manage.py" />
    <option name="PARAMETERS" value="migrate" />
    <option name="SHOW_COMMAND_LINE" value="false" />
    <option name="EMULATE_TERMINAL" value="false" />
    <option name="MODULE_MODE" value="false" />
    <option name="REDIRECT_INPUT" value="false" />
    <option name="INPUT_FILE" value="" />
    <method v="2" />
  </configuration>
</component>