<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Django Server" type="Python.DjangoServer" factoryName="Django server">
    <module name="mcshop" />
    <option name="ENV_FILES" value="" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="PARENT_ENVS" value="true" />
    <envs>
      <env name="PYTHONUNBUFFERED" value="1" />
      <env name="DJANGO_SETTINGS_MODULE" value="mcshop.settings" />
    </envs>
    <option name="SDK_HOME" value="" />
    <option name="SDK_NAME" value="Python 3.11 virtualenv at C:\Users\<USER>\Development\mcshop\venv" />
    <option name="WORKING_DIRECTORY" value="C:\Users\<USER>\Development\mcshop" />
    <option name="IS_MODULE_SDK" value="false" />
    <option name="ADD_CONTENT_ROOTS" value="true" />
    <option name="ADD_SOURCE_ROOTS" value="true" />
    <option name="launchJavascriptDebuger" value="false" />
    <option name="port" value="8000" />
    <option name="host" value="localhost" />
    <option name="additionalOptions" value="" />
    <option name="browserUrl" value="" />
    <option name="runTestServer" value="false" />
    <option name="runNoReload" value="false" />
    <option name="useCustomRunCommand" value="false" />
    <option name="customRunCommand" value="" />
    <method v="2" />
  </configuration>
</component>