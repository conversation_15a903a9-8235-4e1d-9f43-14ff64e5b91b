# Admin Panel Retry Actions

This document describes the retry actions available in the Django admin panel for handling failed command jobs and command_failed purchases.

## Overview

The admin panel now includes retry actions that allow administrators to easily retry failed command executions without needing to use management commands. These actions are available in three different admin interfaces:

1. **Purchase Admin** - Retry failed commands for entire purchases
2. **PurchaseItem Admin** - Retry failed commands for specific purchase items
3. **CommandJob Admin** - Retry individual failed command jobs

## Available Actions

### 1. Purchase Admin - Retry Failed Commands

**Location**: `/admin/shop/purchase/`

**Action**: "Retry failed commands for selected purchases"

**Description**: 
- Retries all failed command jobs for the selected purchases
- Only processes purchases that have at least one failed command job
- Resets failed jobs to pending state and queues the entire purchase for re-execution
- Uses the existing `shop.tasks.execute_purchase_commands` task

**Usage**:
1. Navigate to the Purchase admin list
2. Select one or more purchases (preferably those with `COMMAND_FAILED` state)
3. Choose "Retry failed commands for selected purchases" from the Actions dropdown
4. Click "Go"

**Feedback**:
- Success message: Shows how many purchases were queued for retry
- Warning message: Shows how many purchases were skipped (no failed commands)

### 2. PurchaseItem Admin - Retry Failed Commands

**Location**: `/admin/shop/purchaseitem/`

**Action**: "Retry failed commands for selected purchase items"

**Description**:
- Retries failed command jobs for specific purchase items
- Provides more granular control than the Purchase-level action
- Resets failed jobs to pending state and queues each purchase item for re-execution
- Uses the existing `shop.tasks.execute_purchase_item_commands` task

**Usage**:
1. Navigate to the PurchaseItem admin list
2. Filter by items with failed command states if needed
3. Select one or more purchase items
4. Choose "Retry failed commands for selected purchase items" from the Actions dropdown
5. Click "Go"

### 3. CommandJob Admin - Retry Failed Commands

**Location**: `/admin/shop/commandjob/`

**Action**: "Retry selected failed command jobs"

**Description**:
- Retries individual command jobs
- Provides the most granular control
- Only processes jobs that are in `FAILED` state
- Resets failed jobs to pending state and queues each job individually
- Uses the existing `shop.tasks.execute_single_command` task

**Usage**:
1. Navigate to the CommandJob admin list
2. Filter by "failed" state to see only failed jobs
3. Select one or more failed command jobs
4. Choose "Retry selected failed command jobs" from the Actions dropdown
5. Click "Go"

**Feedback**:
- Success message: Shows how many failed jobs were queued for retry
- Warning message: Shows how many jobs were skipped (not in failed state)

## How It Works

### Retry Process

1. **State Reset**: Failed jobs are reset to `PENDING` state
2. **Data Cleanup**: Error messages, start times, and completion times are cleared
3. **Queue Execution**: Jobs are queued using Django-Q's `async_task` function
4. **Sequential Execution**: Commands execute in their original sequence order
5. **State Updates**: Purchase states are automatically updated based on command results

### Error Handling

- The retry actions only process jobs/items that are actually in failed states
- Non-failed items are skipped and reported in warning messages
- The existing error handling and logging in the task system applies
- If commands fail again, they will be marked as failed and can be retried again

### Integration with Existing System

These admin actions use the same underlying task system as:
- The existing `retry_failed_commands` management command
- The normal purchase command execution flow
- The command queue system documented in `COMMAND_QUEUE_SYSTEM.md`

## Best Practices

1. **Check Command States**: Use the admin list filters to identify failed commands before retrying
2. **Review Error Messages**: Check the error messages in CommandJob records to understand why commands failed
3. **Monitor Execution**: After retrying, monitor the command states to ensure successful execution
4. **Use Appropriate Level**: 
   - Use Purchase-level retry for complete purchase failures
   - Use PurchaseItem-level retry for specific item issues
   - Use CommandJob-level retry for individual command problems

## Related Documentation

- `COMMAND_QUEUE_SYSTEM.md` - Complete documentation of the command queue system
- `shop/management/commands/retry_failed_commands.py` - Command-line retry functionality
- `shop/tasks.py` - Task implementations for command execution
