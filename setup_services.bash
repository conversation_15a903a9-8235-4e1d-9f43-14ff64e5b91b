#!/bin/bash
set -eux

echo "Setting up Django and Django-Q as system services..."

# Make bash scripts executable
chmod +x start.bash
chmod +x start_djangoq.bash

# Copy service files to systemd directory
sudo cp django.service /etc/systemd/system/
sudo cp django-q.service /etc/systemd/system/

# Reload systemd to recognize the new services
sudo systemctl daemon-reload

# Enable services to start on boot
sudo systemctl enable django
sudo systemctl enable django-q

# Start the services
sudo systemctl start django
sudo systemctl start django-q

# Check status
echo "Django service status:"
sudo systemctl status django --no-pager
echo ""
echo "Django-Q service status:"
sudo systemctl status django-q --no-pager

echo ""
echo "Services setup complete!"
echo ""
echo "Useful commands:"
echo "  sudo systemctl start django       # Start Django web server"
echo "  sudo systemctl stop django        # Stop Django web server"
echo "  sudo systemctl restart django     # Restart Django web server"
echo "  sudo systemctl status django      # Check Django status"
echo "  sudo journalctl -u django -f      # View Django live logs"
echo ""
echo "  sudo systemctl start django-q     # Start Django-Q cluster"
echo "  sudo systemctl stop django-q      # Stop Django-Q cluster"
echo "  sudo systemctl restart django-q   # Restart Django-Q cluster"
echo "  sudo systemctl status django-q    # Check Django-Q status"
echo "  sudo journalctl -u django-q -f    # View Django-Q live logs"
echo ""
echo "  sudo systemctl status django django-q  # Check both services"
