#!/usr/bin/env python
"""
Demonstration script for the new user verification functionality
"""
import os
import sys
import django
from unittest.mock import patch, Mock

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.models import MinecraftServer, Category, Item, Purchase, PurchaseItem, CommandJob
from shop.minecraft import is_user_present_on_server
from shop.tasks import execute_purchase_item_commands


def demo_user_verification():
    """Demonstrate the user verification functionality"""
    print("=== User Verification Functionality Demo ===\n")
    
    # Create or get test server
    server, created = MinecraftServer.objects.get_or_create(
        name="demo_server",
        defaults={
            "display_name": "Demo Server",
            "ip": "127.0.0.1",
            "port": 25565,
            "rcon_port": 25575,
            "api_port": 28535,
            "api_token": "demo_token",
            "enabled": True,
            "published": True
        }
    )
    print(f"✅ Server: {server.name} ({'created' if created else 'found'})")
    
    # Create or get test category
    category, created = Category.objects.get_or_create(
        name="demo_category",
        defaults={
            "display_name": "Demo Category",
            "enabled": True,
            "published": True
        }
    )
    print(f"✅ Category: {category.name} ({'created' if created else 'found'})")
    
    # Create items with and without verification
    item_with_verification, created = Item.objects.get_or_create(
        name="verified_diamond",
        defaults={
            "display_name": "Verified Diamond",
            "description": "Diamond that requires user verification",
            "category": category,
            "price": 100,
            "commands": "give {username} diamond 1",
            "minecraft_server": server,
            "require_user_verification": True,
            "enabled": True,
            "published": True
        }
    )
    print(f"✅ Item with verification: {item_with_verification.name} ({'created' if created else 'found'})")
    
    item_without_verification, created = Item.objects.get_or_create(
        name="unverified_gold",
        defaults={
            "display_name": "Unverified Gold",
            "description": "Gold that doesn't require user verification",
            "category": category,
            "price": 50,
            "commands": "give {username} gold_ingot 1",
            "minecraft_server": server,
            "require_user_verification": False,
            "enabled": True,
            "published": True
        }
    )
    print(f"✅ Item without verification: {item_without_verification.name} ({'created' if created else 'found'})")
    
    print("\n=== Testing User Verification Function ===")
    
    # Test 1: User present on server
    print("\n1. Testing user present on server:")
    mock_response_present = Mock()
    mock_response_present.json.return_value = {
        "success": True,
        "user": "online_user",
        "hasPlayedBefore": True
    }
    mock_response_present.raise_for_status.return_value = None
    
    with patch('shop.minecraft.requests.post', return_value=mock_response_present):
        result = is_user_present_on_server("online_user", server)
        print(f"   User 'online_user' present: {result} ✅")
    
    # Test 2: User not present on server
    print("\n2. Testing user not present on server:")
    mock_response_absent = Mock()
    mock_response_absent.json.return_value = {
        "success": True,
        "user": "offline_user",
        "hasPlayedBefore": False
    }
    mock_response_absent.raise_for_status.return_value = None
    
    with patch('shop.minecraft.requests.post', return_value=mock_response_absent):
        result = is_user_present_on_server("offline_user", server)
        print(f"   User 'offline_user' present: {result} ❌")
    
    print("\n=== Testing Command Execution Logic ===")
    
    # Create test purchase
    purchase = Purchase.objects.create(
        minecraft_username="demo_user",
        state=Purchase.State.SUCCESSFUL
    )
    print(f"✅ Created purchase for user: {purchase.minecraft_username}")
    
    # Test 3: Command execution with verification - user present
    print("\n3. Testing command execution with verification (user present):")
    purchase_item_verified = PurchaseItem.objects.create(
        purchase=purchase,
        item=item_with_verification,
        quantity=1
    )
    
    command_job_verified = CommandJob.objects.create(
        purchase_item=purchase_item_verified,
        command_text="give demo_user diamond 1",
        sequence_order=1,
        state=CommandJob.State.PENDING
    )
    
    with patch('shop.tasks.is_user_present_on_server', return_value=True) as mock_user_present:
        with patch('shop.tasks.execute_single_command', return_value=True) as mock_execute:
            execute_purchase_item_commands(purchase_item_verified.id)
            print(f"   ✅ User verification checked: {mock_user_present.called}")
            print(f"   ✅ Command executed: {mock_execute.called}")
    
    # Test 4: Command execution with verification - user not present
    print("\n4. Testing command execution with verification (user not present):")
    purchase_2 = Purchase.objects.create(
        minecraft_username="demo_user_2",
        state=Purchase.State.SUCCESSFUL
    )

    purchase_item_verified_2 = PurchaseItem.objects.create(
        purchase=purchase_2,
        item=item_with_verification,
        quantity=1
    )

    command_job_verified_2 = CommandJob.objects.create(
        purchase_item=purchase_item_verified_2,
        command_text="give demo_user_2 diamond 1",
        sequence_order=1,
        state=CommandJob.State.PENDING
    )

    with patch('shop.tasks.is_user_present_on_server', return_value=False) as mock_user_present:
        with patch('shop.tasks.execute_single_command', return_value=True) as mock_execute:
            execute_purchase_item_commands(purchase_item_verified_2.id)
            print(f"   ✅ User verification checked: {mock_user_present.called}")
            print(f"   ❌ Command NOT executed: {not mock_execute.called}")

            # Check command job state
            command_job_verified_2.refresh_from_db()
            print(f"   ✅ Command job remains PENDING: {command_job_verified_2.state == CommandJob.State.PENDING}")

    # Test 5: Command execution without verification
    print("\n5. Testing command execution without verification:")
    purchase_item_unverified = PurchaseItem.objects.create(
        purchase=purchase,
        item=item_without_verification,
        quantity=1
    )

    command_job_unverified = CommandJob.objects.create(
        purchase_item=purchase_item_unverified,
        command_text="give demo_user gold_ingot 1",
        sequence_order=1,
        state=CommandJob.State.PENDING
    )

    with patch('shop.tasks.execute_single_command', return_value=True) as mock_execute:
        execute_purchase_item_commands(purchase_item_unverified.id)
        print(f"   ✅ Command executed directly: {mock_execute.called}")
    
    print("\n=== Demo Complete ===")
    print("✅ All functionality working as expected!")
    print("\nKey Features:")
    print("- Items can now have 'require_user_verification' field")
    print("- User presence is checked against the item's minecraft_server")
    print("- Commands remain in PENDING state if user verification fails")
    print("- Only one verification check per purchase item (not per command)")
    print("- Existing items without verification work unchanged")


if __name__ == '__main__':
    demo_user_verification()
