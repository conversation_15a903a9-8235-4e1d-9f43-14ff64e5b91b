#!/bin/bash

echo "=== MCShop Services Status ==="
echo ""

echo "Django Web Server:"
sudo systemctl is-active django && echo "✅ Running" || echo "❌ Stopped"
echo ""

echo "Django-Q Task Queue:"
sudo systemctl is-active django-q && echo "✅ Running" || echo "❌ Stopped"
echo ""

echo "Detailed Status:"
echo "=================="
sudo systemctl status django django-q --no-pager

echo ""
echo "Recent logs (last 10 lines):"
echo "============================="
echo "Django logs:"
sudo journalctl -u django -n 10 --no-pager
echo ""
echo "Django-Q logs:"
sudo journalctl -u django-q -n 10 --no-pager
